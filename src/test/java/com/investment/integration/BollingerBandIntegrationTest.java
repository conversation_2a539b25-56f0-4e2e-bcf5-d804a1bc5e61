package com.investment.integration;

import com.investment.api.model.BollingerBandRequest;
import com.investment.api.model.BollingerBandResponse;
import com.investment.database.DatabaseManager;
import com.investment.model.OHLCV;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.AfterEach;
import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.io.File;

/**
 * Integration test for Bollinger Band calculation functionality.
 */
public class BollingerBandIntegrationTest {
    
    private DatabaseManager dbManager;
    
    @BeforeEach
    void setUp() {
        // Clean up any existing test database
        new File("./data/bb_integration_test.duckdb").delete();
        
        // Set up test database
        DatabaseManager.setDbUrl("*********************************************");
        dbManager = new DatabaseManager();
        dbManager.initDatabase();
        
        // Create test data
        setupTestData();
    }
    
    @AfterEach
    void tearDown() {
        if (dbManager != null) {
            dbManager.closeConnection();
        }
        // Clean up test database file
        new File("./data/bb_integration_test.duckdb").delete();
    }
    
    private void setupTestData() {
        try {
            // Create test instruments
            dbManager.saveInstrument("AAPL", "Apple Inc.", "US_STOCK");
            dbManager.saveInstrument("MSFT", "Microsoft Corp.", "US_STOCK");
            dbManager.saveInstrument("NEWCO", "New Company", "US_STOCK");
            
            // Create OHLCV data for AAPL (sufficient data - 25 days)
            List<OHLCV> aaplData = Arrays.asList(
                new OHLCV("AAPL", LocalDate.of(2024, 1, 1), 150.0, 155.0, 148.0, 152.0, 1000000L),
                new OHLCV("AAPL", LocalDate.of(2024, 1, 2), 152.0, 157.0, 150.0, 154.0, 1100000L),
                new OHLCV("AAPL", LocalDate.of(2024, 1, 3), 154.0, 159.0, 152.0, 156.0, 1200000L),
                new OHLCV("AAPL", LocalDate.of(2024, 1, 4), 156.0, 161.0, 154.0, 158.0, 1300000L),
                new OHLCV("AAPL", LocalDate.of(2024, 1, 5), 158.0, 163.0, 156.0, 160.0, 1400000L),
                new OHLCV("AAPL", LocalDate.of(2024, 1, 8), 160.0, 165.0, 158.0, 162.0, 1500000L),
                new OHLCV("AAPL", LocalDate.of(2024, 1, 9), 162.0, 167.0, 160.0, 164.0, 1600000L),
                new OHLCV("AAPL", LocalDate.of(2024, 1, 10), 164.0, 169.0, 162.0, 166.0, 1700000L),
                new OHLCV("AAPL", LocalDate.of(2024, 1, 11), 166.0, 171.0, 164.0, 168.0, 1800000L),
                new OHLCV("AAPL", LocalDate.of(2024, 1, 12), 168.0, 173.0, 166.0, 170.0, 1900000L),
                new OHLCV("AAPL", LocalDate.of(2024, 1, 15), 170.0, 175.0, 168.0, 172.0, 2000000L),
                new OHLCV("AAPL", LocalDate.of(2024, 1, 16), 172.0, 177.0, 170.0, 174.0, 2100000L),
                new OHLCV("AAPL", LocalDate.of(2024, 1, 17), 174.0, 179.0, 172.0, 176.0, 2200000L),
                new OHLCV("AAPL", LocalDate.of(2024, 1, 18), 176.0, 181.0, 174.0, 178.0, 2300000L),
                new OHLCV("AAPL", LocalDate.of(2024, 1, 19), 178.0, 183.0, 176.0, 180.0, 2400000L),
                new OHLCV("AAPL", LocalDate.of(2024, 1, 22), 180.0, 185.0, 178.0, 182.0, 2500000L),
                new OHLCV("AAPL", LocalDate.of(2024, 1, 23), 182.0, 187.0, 180.0, 184.0, 2600000L),
                new OHLCV("AAPL", LocalDate.of(2024, 1, 24), 184.0, 189.0, 182.0, 186.0, 2700000L),
                new OHLCV("AAPL", LocalDate.of(2024, 1, 25), 186.0, 191.0, 184.0, 188.0, 2800000L),
                new OHLCV("AAPL", LocalDate.of(2024, 1, 26), 188.0, 193.0, 186.0, 190.0, 2900000L),
                new OHLCV("AAPL", LocalDate.of(2024, 1, 29), 190.0, 195.0, 188.0, 192.0, 3000000L),
                new OHLCV("AAPL", LocalDate.of(2024, 1, 30), 192.0, 197.0, 190.0, 194.0, 3100000L),
                new OHLCV("AAPL", LocalDate.of(2024, 1, 31), 194.0, 199.0, 192.0, 196.0, 3200000L),
                new OHLCV("AAPL", LocalDate.of(2024, 2, 1), 196.0, 201.0, 194.0, 198.0, 3300000L),
                new OHLCV("AAPL", LocalDate.of(2024, 2, 2), 198.0, 203.0, 196.0, 200.0, 3400000L)
            );
            dbManager.saveOHLCVData(aaplData);
            
            // Create OHLCV data for MSFT (sufficient data - 22 days)
            List<OHLCV> msftData = Arrays.asList(
                new OHLCV("MSFT", LocalDate.of(2024, 1, 1), 300.0, 305.0, 298.0, 302.0, 500000L),
                new OHLCV("MSFT", LocalDate.of(2024, 1, 2), 302.0, 307.0, 300.0, 304.0, 550000L),
                new OHLCV("MSFT", LocalDate.of(2024, 1, 3), 304.0, 309.0, 302.0, 306.0, 600000L),
                new OHLCV("MSFT", LocalDate.of(2024, 1, 4), 306.0, 311.0, 304.0, 308.0, 650000L),
                new OHLCV("MSFT", LocalDate.of(2024, 1, 5), 308.0, 313.0, 306.0, 310.0, 700000L),
                new OHLCV("MSFT", LocalDate.of(2024, 1, 8), 310.0, 315.0, 308.0, 312.0, 750000L),
                new OHLCV("MSFT", LocalDate.of(2024, 1, 9), 312.0, 317.0, 310.0, 314.0, 800000L),
                new OHLCV("MSFT", LocalDate.of(2024, 1, 10), 314.0, 319.0, 312.0, 316.0, 850000L),
                new OHLCV("MSFT", LocalDate.of(2024, 1, 11), 316.0, 321.0, 314.0, 318.0, 900000L),
                new OHLCV("MSFT", LocalDate.of(2024, 1, 12), 318.0, 323.0, 316.0, 320.0, 950000L),
                new OHLCV("MSFT", LocalDate.of(2024, 1, 15), 320.0, 325.0, 318.0, 322.0, 1000000L),
                new OHLCV("MSFT", LocalDate.of(2024, 1, 16), 322.0, 327.0, 320.0, 324.0, 1050000L),
                new OHLCV("MSFT", LocalDate.of(2024, 1, 17), 324.0, 329.0, 322.0, 326.0, 1100000L),
                new OHLCV("MSFT", LocalDate.of(2024, 1, 18), 326.0, 331.0, 324.0, 328.0, 1150000L),
                new OHLCV("MSFT", LocalDate.of(2024, 1, 19), 328.0, 333.0, 326.0, 330.0, 1200000L),
                new OHLCV("MSFT", LocalDate.of(2024, 1, 22), 330.0, 335.0, 328.0, 332.0, 1250000L),
                new OHLCV("MSFT", LocalDate.of(2024, 1, 23), 332.0, 337.0, 330.0, 334.0, 1300000L),
                new OHLCV("MSFT", LocalDate.of(2024, 1, 24), 334.0, 339.0, 332.0, 336.0, 1350000L),
                new OHLCV("MSFT", LocalDate.of(2024, 1, 25), 336.0, 341.0, 334.0, 338.0, 1400000L),
                new OHLCV("MSFT", LocalDate.of(2024, 1, 26), 338.0, 343.0, 336.0, 340.0, 1450000L),
                new OHLCV("MSFT", LocalDate.of(2024, 1, 29), 340.0, 345.0, 338.0, 342.0, 1500000L),
                new OHLCV("MSFT", LocalDate.of(2024, 1, 30), 342.0, 347.0, 340.0, 344.0, 1550000L)
            );
            dbManager.saveOHLCVData(msftData);
            
            // Create OHLCV data for NEWCO (insufficient data - only 5 days)
            List<OHLCV> newcoData = Arrays.asList(
                new OHLCV("NEWCO", LocalDate.of(2024, 1, 1), 10.0, 11.0, 9.5, 10.5, 10000L),
                new OHLCV("NEWCO", LocalDate.of(2024, 1, 2), 10.5, 11.5, 10.0, 11.0, 11000L),
                new OHLCV("NEWCO", LocalDate.of(2024, 1, 3), 11.0, 12.0, 10.5, 11.5, 12000L),
                new OHLCV("NEWCO", LocalDate.of(2024, 1, 4), 11.5, 12.5, 11.0, 12.0, 13000L),
                new OHLCV("NEWCO", LocalDate.of(2024, 1, 5), 12.0, 13.0, 11.5, 12.5, 14000L)
            );
            dbManager.saveOHLCVData(newcoData);
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to setup test data", e);
        }
    }
    
    @Test
    void testBollingerBandCalculationIntegration() {
        try {
            // Test the actual calculation
            BollingerBandRequest request = new BollingerBandRequest(20, 2.0, false);
            request.setMinDataPoints(20);
            
            int aaplUpdated = dbManager.calculateAndUpdateBollingerBands("AAPL", 20, 2.0, false);
            int msftUpdated = dbManager.calculateAndUpdateBollingerBands("MSFT", 20, 2.0, false);
            
            // Verify updates occurred
            assertTrue(aaplUpdated > 0, "AAPL should have updated records");
            assertTrue(msftUpdated > 0, "MSFT should have updated records");
            
            // Verify the data was actually calculated and stored
            List<OHLCV> aaplData = dbManager.getOHLCVData("AAPL", LocalDate.of(2024, 1, 1), LocalDate.of(2024, 2, 2));
            
            // Check that some records now have Bollinger Band data
            boolean foundBollingerData = aaplData.stream()
                    .anyMatch(ohlcv -> ohlcv.getBbMiddleBand() != null);
            
            assertTrue(foundBollingerData, "Should find records with Bollinger Band data");
            
            // Verify the calculation makes sense (middle band should be close to closing prices)
            OHLCV lastRecord = aaplData.get(aaplData.size() - 1);
            if (lastRecord.getBbMiddleBand() != null) {
                assertTrue(lastRecord.getBbMiddleBand() > 0, "Middle band should be positive");
                assertTrue(lastRecord.getBbUpperBand() > lastRecord.getBbMiddleBand(), 
                          "Upper band should be greater than middle band");
                assertTrue(lastRecord.getBbLowerBand() < lastRecord.getBbMiddleBand(), 
                          "Lower band should be less than middle band");
                assertTrue(lastRecord.getBbStdDev() > 0, "Standard deviation should be positive");
            }
            
        } catch (Exception e) {
            fail("Integration test failed: " + e.getMessage());
        }
    }
    
    @Test
    void testDryRunMode() {
        try {
            // Test dry run mode
            int dryRunCount = dbManager.calculateAndUpdateBollingerBands("AAPL", 20, 2.0, true);
            
            // Verify dry run returns count but doesn't update data
            assertTrue(dryRunCount > 0, "Dry run should return count of records that would be updated");
            
            // Verify no actual data was updated
            List<OHLCV> aaplData = dbManager.getOHLCVData("AAPL", LocalDate.of(2024, 1, 1), LocalDate.of(2024, 2, 2));
            boolean foundBollingerData = aaplData.stream()
                    .anyMatch(ohlcv -> ohlcv.getBbMiddleBand() != null);
            
            assertFalse(foundBollingerData, "Dry run should not update actual data");
            
        } catch (Exception e) {
            fail("Dry run test failed: " + e.getMessage());
        }
    }
}
