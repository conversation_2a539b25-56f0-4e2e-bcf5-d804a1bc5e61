package com.investment.model;

import com.investment.database.DatabaseManager;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;

import java.math.BigDecimal;
import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Manual test for the new SQL-based highestAfterTrade calculation.
 * This test uses a real DatabaseManager instance to verify the implementation works correctly.
 * 
 * To run this test:
 * 1. Make sure you have OHLCV data for "AAPL" in your database
 * 2. Run: ./gradlew test --tests "PositionManualTest"
 */
public class PositionManualTest {

    private DatabaseManager databaseManager;

    @BeforeEach
    void setUp() {
        // Initialize with real database connection
        // You may need to adjust the database path based on your setup
        try {
            databaseManager = new DatabaseManager();
            databaseManager.initDatabase();
        } catch (Exception e) {
            System.err.println("Could not initialize database: " + e.getMessage());
            System.err.println("This test requires a real database with OHLCV data");
            databaseManager = null; // Set to null so tests can skip
        }
    }

    @Test
    @DisplayName("Test SQL-based highestAfterTrade calculation for BUY position")
    void testBuyPositionHighestAfterTradeCalculation() {
        // Skip test if database is not available
        if (databaseManager == null) {
            System.out.println("Skipping test - database not available");
            return;
        }

        // Create a BUY position
        Position position = new Position("AAPL", new BigDecimal("100"), Position.Side.BUY, new BigDecimal("150.00"));
        position.setOpenDate(LocalDate.of(2024, 1, 1));
        position.setStatus(Position.Status.OPEN);

        System.out.println("Testing BUY position: " + position);
        System.out.println("Open date: " + position.getOpenDate());

        // Test the SQL-based calculation
        boolean result = position.calculateHighestAfterTradeFromOHLCV(databaseManager);

        System.out.println("Calculation result: " + result);
        System.out.println("HighestAfterTrade value: " + position.getHighestAfterTrade());

        // If we have OHLCV data, the calculation should succeed
        if (result) {
            assertNotNull(position.getHighestAfterTrade(), "HighestAfterTrade should not be null when calculation succeeds");
            assertTrue(position.getHighestAfterTrade().compareTo(BigDecimal.ZERO) > 0, "HighestAfterTrade should be positive");
            System.out.println("✅ BUY position test passed - highestAfterTrade calculated from MAX(high): " + position.getHighestAfterTrade());
        } else {
            System.out.println("⚠️ No OHLCV data found for AAPL starting from 2024-01-01");
        }
    }

    @Test
    @DisplayName("Test SQL-based highestAfterTrade calculation for SELL position")
    void testSellPositionHighestAfterTradeCalculation() {
        // Skip test if database is not available
        if (databaseManager == null) {
            System.out.println("Skipping test - database not available");
            return;
        }

        // Create a SELL position
        Position position = new Position("AAPL", new BigDecimal("-100"), Position.Side.SELL, new BigDecimal("150.00"));
        position.setOpenDate(LocalDate.of(2024, 1, 1));
        position.setCloseDate(LocalDate.of(2024, 1, 31)); // Closed position
        position.setStatus(Position.Status.CLOSED);

        System.out.println("Testing SELL position: " + position);
        System.out.println("Date range: " + position.getOpenDate() + " to " + position.getCloseDate());

        // Test the SQL-based calculation
        boolean result = position.calculateHighestAfterTradeFromOHLCV(databaseManager);

        System.out.println("Calculation result: " + result);
        System.out.println("HighestAfterTrade value: " + position.getHighestAfterTrade());

        // If we have OHLCV data, the calculation should succeed
        if (result) {
            assertNotNull(position.getHighestAfterTrade(), "HighestAfterTrade should not be null when calculation succeeds");
            assertTrue(position.getHighestAfterTrade().compareTo(BigDecimal.ZERO) > 0, "HighestAfterTrade should be positive");
            System.out.println("✅ SELL position test passed - highestAfterTrade calculated from MIN(low): " + position.getHighestAfterTrade());
        } else {
            System.out.println("⚠️ No OHLCV data found for AAPL in date range 2024-01-01 to 2024-01-31");
        }
    }

    @Test
    @DisplayName("Test updateMarketDataWithOHLCVAndDatabase method")
    void testUpdateMarketDataWithOHLCVAndDatabase() {
        // Skip test if database is not available
        if (databaseManager == null) {
            System.out.println("Skipping test - database not available");
            return;
        }

        // Create a BUY position
        Position position = new Position("AAPL", new BigDecimal("100"), Position.Side.BUY, new BigDecimal("150.00"));
        position.setOpenDate(LocalDate.of(2024, 1, 1));
        position.setStatus(Position.Status.OPEN);

        System.out.println("Testing updateMarketDataWithOHLCVAndDatabase: " + position);

        // Test the enhanced update method
        position.updateMarketDataWithOHLCVAndDatabase(
            new BigDecimal("155.00"), // close price
            new BigDecimal("158.00"), // high price (current period)
            new BigDecimal("154.00"), // low price (current period)
            databaseManager
        );

        System.out.println("After update:");
        System.out.println("Last price: " + position.getLastPrice());
        System.out.println("P&L value: " + position.getPnlValue());
        System.out.println("HighestAfterTrade: " + position.getHighestAfterTrade());

        // Verify basic updates
        assertEquals(new BigDecimal("155.00"), position.getLastPrice(), "Last price should be updated");
        assertEquals(new BigDecimal("500.00"), position.getPnlValue(), "P&L should be calculated correctly");

        // HighestAfterTrade should be set (either from SQL query or fallback)
        assertNotNull(position.getHighestAfterTrade(), "HighestAfterTrade should be set");

        System.out.println("✅ updateMarketDataWithOHLCVAndDatabase test completed");
    }

    @Test
    @DisplayName("Test direct database methods")
    void testDatabaseMethods() {
        // Skip test if database is not available
        if (databaseManager == null) {
            System.out.println("Skipping test - database not available");
            return;
        }

        System.out.println("Testing direct database methods...");

        // Test getMaxHighPrice
        BigDecimal maxHigh = databaseManager.getMaxHighPrice("AAPL", LocalDate.of(2024, 1, 1), null);
        System.out.println("Max high price for AAPL since 2024-01-01: " + maxHigh);

        // Test getMinLowPrice
        BigDecimal minLow = databaseManager.getMinLowPrice("AAPL", LocalDate.of(2024, 1, 1), LocalDate.of(2024, 1, 31));
        System.out.println("Min low price for AAPL in Jan 2024: " + minLow);

        if (maxHigh != null) {
            System.out.println("✅ getMaxHighPrice working correctly");
        } else {
            System.out.println("⚠️ No OHLCV data found for getMaxHighPrice query");
        }

        if (minLow != null) {
            System.out.println("✅ getMinLowPrice working correctly");
        } else {
            System.out.println("⚠️ No OHLCV data found for getMinLowPrice query");
        }
    }
}
