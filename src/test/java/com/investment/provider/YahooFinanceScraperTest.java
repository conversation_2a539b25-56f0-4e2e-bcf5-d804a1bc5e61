package com.investment.provider;

import com.investment.model.OHLCV;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

class YahooFinanceScraperTest {

    @Test
    @Disabled("This test requires an internet connection and may fail if Yahoo Finance changes its HTML structure")
    void testScrapeHistoricalData() throws IOException {
        // This is a simple integration test that verifies the scraper can connect to Yahoo Finance
        // and extract data. It's not a unit test as it depends on an external service.

        YahooFinanceScraper scraper = new YahooFinanceScraper();

        // Use a well-known stock and a short date range
        String symbol = "AAPL";
        LocalDate startDate = LocalDate.now().minusDays(7);
        LocalDate endDate = LocalDate.now();

        long period1 = startDate.atStartOfDay().toEpochSecond(java.time.ZoneOffset.UTC);
        long period2 = endDate.plusDays(1).atStartOfDay().toEpochSecond(java.time.ZoneOffset.UTC);

        List<OHLCV> results = scraper.scrapeHistoricalData(symbol, period1, period2);

        // We might not get data for weekends or holidays, but we should get some data
        // if the scraper is working correctly
        assertFalse(results.isEmpty(), "Should have retrieved at least some data points");

        // Verify the data structure
        for (OHLCV data : results) {
            assertEquals(symbol, data.getSymbol(), "Symbol should match");
            assertTrue(data.getDate().isAfter(startDate.minusDays(1)), "Date should be after start date");
            assertTrue(data.getDate().isBefore(endDate.plusDays(1)), "Date should be before end date");
            assertTrue(data.getOpen() > 0, "Open price should be positive");
            assertTrue(data.getHigh() > 0, "High price should be positive");
            assertTrue(data.getLow() > 0, "Low price should be positive");
            assertTrue(data.getClose() > 0, "Close price should be positive");
            assertTrue(data.getVolume() > 0, "Volume should be positive");
        }
    }

    @Test
    void testMockData() {
        // This is a simple test to verify that the test suite can run without external dependencies
        List<OHLCV> mockData = new ArrayList<>();
        mockData.add(new OHLCV("AAPL", LocalDate.now(), 100.0, 110.0, 90.0, 105.0, 1000));

        assertFalse(mockData.isEmpty(), "Mock data should not be empty");
        assertEquals("AAPL", mockData.get(0).getSymbol(), "Symbol should match");
        assertTrue(mockData.get(0).getOpen() > 0, "Open price should be positive");
    }

    @Test
    @Disabled("This test requires an internet connection and may fail if Yahoo Finance changes its HTML structure")
    void testHtmlFileSaving() throws IOException {
        // This test verifies that HTML files are being saved to the filesystem
        YahooFinanceScraper scraper = new YahooFinanceScraper();

        // Use a well-known stock and a short date range
        String symbol = "AAPL";
        LocalDate startDate = LocalDate.now().minusDays(1); // Just one day to minimize data
        LocalDate endDate = LocalDate.now();

        long period1 = startDate.atStartOfDay().toEpochSecond(java.time.ZoneOffset.UTC);
        long period2 = endDate.plusDays(1).atStartOfDay().toEpochSecond(java.time.ZoneOffset.UTC);

        // Get the download directory path
        String downloadDir = System.getProperty("user.home") + "/Downloads/investhtml";
        Path dirPath = Paths.get(downloadDir);

        // Count HTML files before the test
        int fileCountBefore = 0;
        if (Files.exists(dirPath)) {
            fileCountBefore = countHtmlFiles(dirPath, symbol);
        }

        // Call the method that should save HTML files
        scraper.scrapeHistoricalData(symbol, period1, period2);

        // Verify that the directory exists
        assertTrue(Files.exists(dirPath), "HTML storage directory should exist");

        // Count HTML files after the test
        int fileCountAfter = countHtmlFiles(dirPath, symbol);

        // Verify that at least one new file was created
        assertTrue(fileCountAfter > fileCountBefore,
                "At least one new HTML file should have been created (before: " + fileCountBefore + ", after: " + fileCountAfter + ")");
    }

    private int countHtmlFiles(Path dirPath, String symbol) throws IOException {
        // Count HTML files for the given symbol
        return (int) Files.list(dirPath)
                .filter(path -> path.getFileName().toString().startsWith("yahoo_finance_" + symbol))
                .filter(path -> path.getFileName().toString().endsWith(".html"))
                .count();
    }
}
