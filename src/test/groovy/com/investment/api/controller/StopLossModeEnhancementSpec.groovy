package com.investment.api.controller

import com.investment.api.model.PnLRecalculationRequest
import com.investment.api.model.PositionsUpdateRequest
import spock.lang.Specification

class StopLossModeEnhancementSpec extends Specification {

    def "should support STANDARD stop-loss mode in PositionsUpdateRequest"() {
        given: "a positions update request with STANDARD mode"
        def request = new PositionsUpdateRequest(false, true, PositionsUpdateRequest.StopLossMode.STANDARD)

        expect: "the request should have correct stop-loss mode"
        request.stopLossMode == PositionsUpdateRequest.StopLossMode.STANDARD
        request.toString().contains("stopLossMode=STANDARD")
    }

    def "should support ENHANCED stop-loss mode in PositionsUpdateRequest"() {
        given: "a positions update request with ENHANCED mode"
        def request = new PositionsUpdateRequest(false, true, PositionsUpdateRequest.StopLossMode.ENHANCED)

        expect: "the request should have correct stop-loss mode"
        request.stopLossMode == PositionsUpdateRequest.StopLossMode.ENHANCED
        request.toString().contains("stopLossMode=ENHANCED")
    }

    def "should support STANDARD stop-loss mode in PnLRecalculationRequest"() {
        given: "a P&L recalculation request with STANDARD mode"
        def request = new PnLRecalculationRequest(PnLRecalculationRequest.StopLossMode.STANDARD)

        expect: "the request should have correct stop-loss mode"
        request.stopLossMode == PnLRecalculationRequest.StopLossMode.STANDARD
        request.toString().contains("stopLossMode=STANDARD")
    }

    def "should support ENHANCED stop-loss mode in PnLRecalculationRequest"() {
        given: "a P&L recalculation request with ENHANCED mode"
        def request = new PnLRecalculationRequest(PnLRecalculationRequest.StopLossMode.ENHANCED)

        expect: "the request should have correct stop-loss mode"
        request.stopLossMode == PnLRecalculationRequest.StopLossMode.ENHANCED
        request.toString().contains("stopLossMode=ENHANCED")
    }

    def "should default to STANDARD mode when no mode specified"() {
        given: "requests with default constructors"
        def updateRequest = new PositionsUpdateRequest()
        def pnlRequest = new PnLRecalculationRequest()

        expect: "both should default to STANDARD mode"
        updateRequest.stopLossMode == PositionsUpdateRequest.StopLossMode.STANDARD
        pnlRequest.stopLossMode == PnLRecalculationRequest.StopLossMode.STANDARD
    }

    def "should validate stop-loss mode enum values"() {
        expect: "enum values should be correctly defined"
        PositionsUpdateRequest.StopLossMode.values().length == 2
        PositionsUpdateRequest.StopLossMode.STANDARD.name() == "STANDARD"
        PositionsUpdateRequest.StopLossMode.ENHANCED.name() == "ENHANCED"

        PnLRecalculationRequest.StopLossMode.values().length == 2
        PnLRecalculationRequest.StopLossMode.STANDARD.name() == "STANDARD"
        PnLRecalculationRequest.StopLossMode.ENHANCED.name() == "ENHANCED"
    }
}
