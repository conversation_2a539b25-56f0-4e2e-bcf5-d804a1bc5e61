package com.investment.api.model;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Response model for SEC synchronization operations.
 */
public class SyncResponse {
    private final int totalSecSymbols;
    private final int existingInDatabase;
    private final int missingFromDatabase;
    private final List<String> missingSymbolsList;
    private final int addedSymbols;
    private final boolean dryRun;
    private final LocalDateTime timestamp;
    private final String summary;
    
    public SyncResponse(int totalSecSymbols, int existingInDatabase, int missingFromDatabase,
                       List<String> missingSymbolsList, int addedSymbols, boolean dryRun) {
        this.totalSecSymbols = totalSecSymbols;
        this.existingInDatabase = existingInDatabase;
        this.missingFromDatabase = missingFromDatabase;
        this.missingSymbolsList = missingSymbolsList;
        this.addedSymbols = addedSymbols;
        this.dryRun = dryRun;
        this.timestamp = LocalDateTime.now();
        this.summary = generateSummary();
    }
    
    private String generateSummary() {
        if (dryRun) {
            return String.format("DRY RUN: Found %d missing symbols out of %d SEC symbols. " +
                    "Would add %d new instruments to database.",
                    missingFromDatabase, totalSecSymbols, missingFromDatabase);
        } else {
            return String.format("SYNC COMPLETED: Added %d new instruments out of %d missing symbols. " +
                    "Database now has %d instruments from SEC data.",
                    addedSymbols, missingFromDatabase, existingInDatabase + addedSymbols);
        }
    }
    
    public int getTotalSecSymbols() {
        return totalSecSymbols;
    }
    
    public int getExistingInDatabase() {
        return existingInDatabase;
    }
    
    public int getMissingFromDatabase() {
        return missingFromDatabase;
    }
    
    public List<String> getMissingSymbolsList() {
        return missingSymbolsList;
    }
    
    public int getAddedSymbols() {
        return addedSymbols;
    }
    
    public boolean isDryRun() {
        return dryRun;
    }
    
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
    
    public String getSummary() {
        return summary;
    }
    
    @Override
    public String toString() {
        return "SyncResponse{" +
                "totalSecSymbols=" + totalSecSymbols +
                ", existingInDatabase=" + existingInDatabase +
                ", missingFromDatabase=" + missingFromDatabase +
                ", missingSymbolsList=" + missingSymbolsList +
                ", addedSymbols=" + addedSymbols +
                ", dryRun=" + dryRun +
                ", timestamp=" + timestamp +
                ", summary='" + summary + '\'' +
                '}';
    }
}
