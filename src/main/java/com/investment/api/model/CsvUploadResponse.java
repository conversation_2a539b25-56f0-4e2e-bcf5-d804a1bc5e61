package com.investment.api.model;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Response model for CSV file upload operations.
 */
public class CsvUploadResponse {
    private final int totalRowsInCsv;
    private final int validRows;
    private final int invalidRows;
    private final int processedInstruments;
    private final int skippedInstruments;
    private final int addedInstruments;
    private final int updatedInstruments;
    private final List<String> validationErrors;
    private final List<String> processedSymbols;
    private final boolean dryRun;
    private final LocalDateTime timestamp;
    private final String summary;
    
    public CsvUploadResponse(int totalRowsInCsv, int validRows, int invalidRows,
                           int processedInstruments, int skippedInstruments,
                           int addedInstruments, int updatedInstruments,
                           List<String> validationErrors, List<String> processedSymbols,
                           boolean dryRun) {
        this.totalRowsInCsv = totalRowsInCsv;
        this.validRows = validRows;
        this.invalidRows = invalidRows;
        this.processedInstruments = processedInstruments;
        this.skippedInstruments = skippedInstruments;
        this.addedInstruments = addedInstruments;
        this.updatedInstruments = updatedInstruments;
        this.validationErrors = validationErrors;
        this.processedSymbols = processedSymbols;
        this.dryRun = dryRun;
        this.timestamp = LocalDateTime.now();
        this.summary = generateSummary();
    }
    
    private String generateSummary() {
        if (dryRun) {
            return String.format("DRY RUN: Processed %d valid rows out of %d total rows. " +
                    "Would add/update %d instruments. Found %d validation errors.",
                    validRows, totalRowsInCsv, processedInstruments, validationErrors.size());
        } else {
            return String.format("CSV UPLOAD COMPLETED: Processed %d instruments from %d valid rows. " +
                    "Added %d new instruments, updated %d existing instruments, skipped %d duplicates.",
                    processedInstruments, validRows, addedInstruments, updatedInstruments, skippedInstruments);
        }
    }
    
    public int getTotalRowsInCsv() {
        return totalRowsInCsv;
    }
    
    public int getValidRows() {
        return validRows;
    }
    
    public int getInvalidRows() {
        return invalidRows;
    }
    
    public int getProcessedInstruments() {
        return processedInstruments;
    }
    
    public int getSkippedInstruments() {
        return skippedInstruments;
    }
    
    public int getAddedInstruments() {
        return addedInstruments;
    }
    
    public int getUpdatedInstruments() {
        return updatedInstruments;
    }
    
    public List<String> getValidationErrors() {
        return validationErrors;
    }
    
    public List<String> getProcessedSymbols() {
        return processedSymbols;
    }
    
    public boolean isDryRun() {
        return dryRun;
    }
    
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
    
    public String getSummary() {
        return summary;
    }
    
    @Override
    public String toString() {
        return "CsvUploadResponse{" +
                "totalRowsInCsv=" + totalRowsInCsv +
                ", validRows=" + validRows +
                ", invalidRows=" + invalidRows +
                ", processedInstruments=" + processedInstruments +
                ", skippedInstruments=" + skippedInstruments +
                ", addedInstruments=" + addedInstruments +
                ", updatedInstruments=" + updatedInstruments +
                ", validationErrors=" + validationErrors +
                ", processedSymbols=" + processedSymbols +
                ", dryRun=" + dryRun +
                ", timestamp=" + timestamp +
                ", summary='" + summary + '\'' +
                '}';
    }
}
