package com.investment.api.model;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Request model for P&L recalculation operations.
 */
@Schema(description = "Request for P&L recalculation operations")
public class PnLRecalculationRequest {

    @Schema(description = "Stop-loss calculation mode to use", 
            example = "STANDARD", defaultValue = "STANDARD")
    private StopLossMode stopLossMode = StopLossMode.STANDARD;

    /**
     * Enum for stop-loss calculation modes.
     */
    public enum StopLossMode {
        @Schema(description = "Standard stop-loss calculation using basic parameters")
        STANDARD,
        @Schema(description = "Enhanced stop-loss calculation using dynamic risk management")
        ENHANCED
    }

    public PnLRecalculationRequest() {
    }

    public PnLRecalculationRequest(StopLossMode stopLossMode) {
        this.stopLossMode = stopLossMode;
    }

    public StopLossMode getStopLossMode() {
        return stopLossMode;
    }

    public void setStopLossMode(StopLossMode stopLossMode) {
        this.stopLossMode = stopLossMode;
    }

    @Override
    public String toString() {
        return "PnLRecalculationRequest{" +
                "stopLossMode=" + stopLossMode +
                '}';
    }
}
