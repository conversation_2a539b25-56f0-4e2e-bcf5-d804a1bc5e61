package com.investment.api.model;

import com.investment.model.WatchListItem;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Response model for watch list item data.
 */
@Schema(description = "Watch list item response")
public class WatchListResponse {

    @Schema(description = "Unique identifier", example = "1")
    private Long id;

    @Schema(description = "Display index for custom ordering", example = "1")
    private Integer displayIndex;

    @Schema(description = "Financial instrument symbol", example = "AAPL")
    private String symbol;

    @Schema(description = "Date when symbol was added to watch list", example = "2024-01-15")
    private LocalDate startDate;

    @Schema(description = "Optional notes/comments about the symbol", example = "Strong growth potential")
    private String remarks;

    @Schema(description = "1-month performance percentage", example = "0.0523")
    private BigDecimal oneMonthPerf;

    @Schema(description = "3-month performance percentage", example = "0.1245")
    private BigDecimal threeMonthPerf;

    @Schema(description = "6-month performance percentage", example = "0.2187")
    private BigDecimal sixMonthPerf;

    @Schema(description = "Consecutive days of bullish Bollinger Band signals", example = "5")
    private Integer bullishBbStreak;

    @Schema(description = "Consecutive days of bullish DMI signals", example = "3")
    private Integer dmiBullishStreak;

    @Schema(description = "Consecutive days of combined BUY signals", example = "2")
    private Integer combinedSignalStreak;

    @Schema(description = "When the item was created", example = "2024-01-15T10:30:00")
    private LocalDateTime createdDate;

    @Schema(description = "When the item was last updated", example = "2024-01-16T14:45:00")
    private LocalDateTime updatedDate;

    /**
     * Default constructor.
     */
    public WatchListResponse() {
    }

    /**
     * Create response from domain model.
     */
    public static WatchListResponse fromWatchListItem(WatchListItem item) {
        WatchListResponse response = new WatchListResponse();
        response.setId(item.getId());
        response.setDisplayIndex(item.getDisplayIndex());
        response.setSymbol(item.getSymbol());
        response.setStartDate(item.getStartDate());
        response.setRemarks(item.getRemarks());
        response.setOneMonthPerf(item.getOneMonthPerf());
        response.setThreeMonthPerf(item.getThreeMonthPerf());
        response.setSixMonthPerf(item.getSixMonthPerf());
        response.setBullishBbStreak(item.getBullishBbStreak());
        response.setDmiBullishStreak(item.getDmiBullishStreak());
        response.setCombinedSignalStreak(item.getCombinedSignalStreak());
        response.setCreatedDate(item.getCreatedDate());
        response.setUpdatedDate(item.getUpdatedDate());
        return response;
    }

    // Getters and Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getDisplayIndex() {
        return displayIndex;
    }

    public void setDisplayIndex(Integer displayIndex) {
        this.displayIndex = displayIndex;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public BigDecimal getOneMonthPerf() {
        return oneMonthPerf;
    }

    public void setOneMonthPerf(BigDecimal oneMonthPerf) {
        this.oneMonthPerf = oneMonthPerf;
    }

    public BigDecimal getThreeMonthPerf() {
        return threeMonthPerf;
    }

    public void setThreeMonthPerf(BigDecimal threeMonthPerf) {
        this.threeMonthPerf = threeMonthPerf;
    }

    public BigDecimal getSixMonthPerf() {
        return sixMonthPerf;
    }

    public void setSixMonthPerf(BigDecimal sixMonthPerf) {
        this.sixMonthPerf = sixMonthPerf;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public Integer getBullishBbStreak() {
        return bullishBbStreak;
    }

    public void setBullishBbStreak(Integer bullishBbStreak) {
        this.bullishBbStreak = bullishBbStreak;
    }

    public Integer getDmiBullishStreak() {
        return dmiBullishStreak;
    }

    public void setDmiBullishStreak(Integer dmiBullishStreak) {
        this.dmiBullishStreak = dmiBullishStreak;
    }

    public Integer getCombinedSignalStreak() {
        return combinedSignalStreak;
    }

    public void setCombinedSignalStreak(Integer combinedSignalStreak) {
        this.combinedSignalStreak = combinedSignalStreak;
    }

    @Override
    public String toString() {
        return "WatchListResponse{" +
                "id=" + id +
                ", displayIndex=" + displayIndex +
                ", symbol='" + symbol + '\'' +
                ", startDate=" + startDate +
                ", remarks='" + remarks + '\'' +
                ", oneMonthPerf=" + oneMonthPerf +
                ", threeMonthPerf=" + threeMonthPerf +
                ", sixMonthPerf=" + sixMonthPerf +
                ", bullishBbStreak=" + bullishBbStreak +
                ", dmiBullishStreak=" + dmiBullishStreak +
                ", combinedSignalStreak=" + combinedSignalStreak +
                ", createdDate=" + createdDate +
                ", updatedDate=" + updatedDate +
                '}';
    }
}
