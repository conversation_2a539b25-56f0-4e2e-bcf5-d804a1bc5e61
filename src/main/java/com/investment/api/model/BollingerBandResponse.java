package com.investment.api.model;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Response model for Bollinger Band calculation operations.
 */
@Schema(description = "Response containing results of Bollinger Band calculation operation")
public class BollingerBandResponse {
    
    @Schema(description = "Operation status", example = "success")
    private final String status;
    
    @Schema(description = "Number of symbols processed", example = "150")
    private final int processedSymbols;
    
    @Schema(description = "Total number of OHLCV records updated with Bollinger Band data", example = "45000")
    private final int totalRecordsUpdated;
    
    @Schema(description = "List of symbols that had insufficient data for calculation")
    private final List<String> symbolsWithInsufficientData;
    
    @Schema(description = "List of symbols that were skipped (already have data and forceRecalculate=false)")
    private final List<String> skippedSymbols;
    
    @Schema(description = "List of symbols that failed during calculation")
    private final List<String> failedSymbols;
    
    @Schema(description = "Processing time in milliseconds", example = "2500")
    private final long processingTimeMs;
    
    @Schema(description = "List of error messages encountered during processing")
    private final List<String> errors;
    
    @Schema(description = "Whether this was a dry run (no actual updates performed)", example = "false")
    private final boolean dryRun;
    
    @Schema(description = "Calculation parameters used")
    private final CalculationParameters parameters;
    
    @Schema(description = "Timestamp when the operation completed")
    private final LocalDateTime timestamp;
    
    @Schema(description = "Summary of the operation")
    private final String summary;

    public BollingerBandResponse(String status, int processedSymbols, int totalRecordsUpdated,
                               List<String> symbolsWithInsufficientData, List<String> skippedSymbols,
                               List<String> failedSymbols, long processingTimeMs, List<String> errors,
                               boolean dryRun, CalculationParameters parameters) {
        this.status = status;
        this.processedSymbols = processedSymbols;
        this.totalRecordsUpdated = totalRecordsUpdated;
        this.symbolsWithInsufficientData = symbolsWithInsufficientData;
        this.skippedSymbols = skippedSymbols;
        this.failedSymbols = failedSymbols;
        this.processingTimeMs = processingTimeMs;
        this.errors = errors;
        this.dryRun = dryRun;
        this.parameters = parameters;
        this.timestamp = LocalDateTime.now();
        this.summary = generateSummary();
    }

    private String generateSummary() {
        if (dryRun) {
            return String.format("Dry run completed: %d symbols would be processed, %d records would be updated",
                    processedSymbols, totalRecordsUpdated);
        } else {
            return String.format("Bollinger Band calculation completed: %d symbols processed, %d records updated in %dms",
                    processedSymbols, totalRecordsUpdated, processingTimeMs);
        }
    }

    // Getters
    public String getStatus() { return status; }
    public int getProcessedSymbols() { return processedSymbols; }
    public int getTotalRecordsUpdated() { return totalRecordsUpdated; }
    public List<String> getSymbolsWithInsufficientData() { return symbolsWithInsufficientData; }
    public List<String> getSkippedSymbols() { return skippedSymbols; }
    public List<String> getFailedSymbols() { return failedSymbols; }
    public long getProcessingTimeMs() { return processingTimeMs; }
    public List<String> getErrors() { return errors; }
    public boolean isDryRun() { return dryRun; }
    public CalculationParameters getParameters() { return parameters; }
    public LocalDateTime getTimestamp() { return timestamp; }
    public String getSummary() { return summary; }

    /**
     * Nested class for calculation parameters.
     */
    @Schema(description = "Parameters used for Bollinger Band calculation")
    public static class CalculationParameters {
        @Schema(description = "Number of periods for moving average", example = "20")
        private final int period;

        @Schema(description = "Standard deviation multiplier", example = "2.0")
        private final double stdDevMultiplier;

        @Schema(description = "Minimum data points required", example = "20")
        private final int minDataPoints;

        @Schema(description = "Calculation mode used", example = "INCREMENTAL")
        private final BollingerBandRequest.CalculationMode calculationMode;

        public CalculationParameters(int period, double stdDevMultiplier, int minDataPoints, BollingerBandRequest.CalculationMode calculationMode) {
            this.period = period;
            this.stdDevMultiplier = stdDevMultiplier;
            this.minDataPoints = minDataPoints;
            this.calculationMode = calculationMode;
        }

        public int getPeriod() { return period; }
        public double getStdDevMultiplier() { return stdDevMultiplier; }
        public int getMinDataPoints() { return minDataPoints; }
        public BollingerBandRequest.CalculationMode getCalculationMode() { return calculationMode; }
    }
}
