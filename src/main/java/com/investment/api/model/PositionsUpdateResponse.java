package com.investment.api.model;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Response model for positions OHLCV update operations.
 */
@Schema(description = "Response for positions OHLCV update operations")
public class PositionsUpdateResponse {

    @Schema(description = "Total number of unique symbols processed", example = "15")
    private int totalSymbols;

    @Schema(description = "Number of symbols with successful OHLCV updates", example = "14")
    private int ohlcvSuccessCount;

    @Schema(description = "Number of symbols with failed OHLCV updates", example = "1")
    private int ohlcvErrorCount;

    @Schema(description = "Total number of OHLCV records updated", example = "142")
    private int totalRecordsUpdated;

    @Schema(description = "Number of positions with updated P&L", example = "25")
    private int pnlUpdatedCount;

    @Schema(description = "Total number of positions processed", example = "25")
    private int totalPositions;

    @Schema(description = "Processing time in milliseconds", example = "45000")
    private long processingTimeMs;

    @Schema(description = "Detailed results for each symbol")
    private List<SymbolUpdateResult> ohlcvResults;

    @Schema(description = "Summary of the update operation")
    private String summary;

    public PositionsUpdateResponse() {
    }

    public PositionsUpdateResponse(int totalSymbols, int ohlcvSuccessCount, int ohlcvErrorCount,
                                 int totalRecordsUpdated, int pnlUpdatedCount, int totalPositions,
                                 long processingTimeMs, List<SymbolUpdateResult> ohlcvResults, String summary) {
        this.totalSymbols = totalSymbols;
        this.ohlcvSuccessCount = ohlcvSuccessCount;
        this.ohlcvErrorCount = ohlcvErrorCount;
        this.totalRecordsUpdated = totalRecordsUpdated;
        this.pnlUpdatedCount = pnlUpdatedCount;
        this.totalPositions = totalPositions;
        this.processingTimeMs = processingTimeMs;
        this.ohlcvResults = ohlcvResults;
        this.summary = summary;
    }

    // Getters and setters
    public int getTotalSymbols() {
        return totalSymbols;
    }

    public void setTotalSymbols(int totalSymbols) {
        this.totalSymbols = totalSymbols;
    }

    public int getOhlcvSuccessCount() {
        return ohlcvSuccessCount;
    }

    public void setOhlcvSuccessCount(int ohlcvSuccessCount) {
        this.ohlcvSuccessCount = ohlcvSuccessCount;
    }

    public int getOhlcvErrorCount() {
        return ohlcvErrorCount;
    }

    public void setOhlcvErrorCount(int ohlcvErrorCount) {
        this.ohlcvErrorCount = ohlcvErrorCount;
    }

    public int getTotalRecordsUpdated() {
        return totalRecordsUpdated;
    }

    public void setTotalRecordsUpdated(int totalRecordsUpdated) {
        this.totalRecordsUpdated = totalRecordsUpdated;
    }

    public int getPnlUpdatedCount() {
        return pnlUpdatedCount;
    }

    public void setPnlUpdatedCount(int pnlUpdatedCount) {
        this.pnlUpdatedCount = pnlUpdatedCount;
    }

    public int getTotalPositions() {
        return totalPositions;
    }

    public void setTotalPositions(int totalPositions) {
        this.totalPositions = totalPositions;
    }

    public long getProcessingTimeMs() {
        return processingTimeMs;
    }

    public void setProcessingTimeMs(long processingTimeMs) {
        this.processingTimeMs = processingTimeMs;
    }

    public List<SymbolUpdateResult> getOhlcvResults() {
        return ohlcvResults;
    }

    public void setOhlcvResults(List<SymbolUpdateResult> ohlcvResults) {
        this.ohlcvResults = ohlcvResults;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    /**
     * Result for individual symbol update operation.
     */
    @Schema(description = "Result for individual symbol update operation")
    public static class SymbolUpdateResult {
        @Schema(description = "Symbol that was processed", example = "AAPL")
        private String symbol;

        @Schema(description = "Status of the update", example = "success")
        private String status;

        @Schema(description = "Success message if applicable", example = "Updated 5 records")
        private String message;

        @Schema(description = "Error message if applicable")
        private String error;

        public SymbolUpdateResult() {
        }

        public SymbolUpdateResult(String symbol, String status, String message) {
            this.symbol = symbol;
            this.status = status;
            this.message = message;
        }

        public SymbolUpdateResult(String symbol, String status, String message, String error) {
            this.symbol = symbol;
            this.status = status;
            this.message = message;
            this.error = error;
        }

        // Getters and setters
        public String getSymbol() {
            return symbol;
        }

        public void setSymbol(String symbol) {
            this.symbol = symbol;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getError() {
            return error;
        }

        public void setError(String error) {
            this.error = error;
        }
    }
}
