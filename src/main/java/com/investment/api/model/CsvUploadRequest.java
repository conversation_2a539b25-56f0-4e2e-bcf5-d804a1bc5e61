package com.investment.api.model;

/**
 * Request model for CSV file upload operations.
 */
public class CsvUploadRequest {
    private boolean dryRun = true;
    private int maxInstruments = 1000; // Limit to prevent overwhelming the system
    private boolean skipDuplicates = true; // Skip instruments that already exist
    private boolean validateData = true; // Perform data validation
    
    public CsvUploadRequest() {}
    
    public CsvUploadRequest(boolean dryRun, int maxInstruments, boolean skipDuplicates, boolean validateData) {
        this.dryRun = dryRun;
        this.maxInstruments = maxInstruments;
        this.skipDuplicates = skipDuplicates;
        this.validateData = validateData;
    }
    
    /**
     * Whether to perform a dry run (no actual insertions).
     * @return true if this is a dry run, false for actual processing
     */
    public boolean isDryRun() {
        return dryRun;
    }
    
    public void setDryRun(boolean dryRun) {
        this.dryRun = dryRun;
    }
    
    /**
     * Maximum number of instruments to process in a single operation.
     * @return maximum number of instruments to process
     */
    public int getMaxInstruments() {
        return maxInstruments;
    }
    
    public void setMaxInstruments(int maxInstruments) {
        this.maxInstruments = maxInstruments;
    }
    
    /**
     * Whether to skip instruments that already exist in the database.
     * @return true to skip duplicates, false to update existing instruments
     */
    public boolean isSkipDuplicates() {
        return skipDuplicates;
    }
    
    public void setSkipDuplicates(boolean skipDuplicates) {
        this.skipDuplicates = skipDuplicates;
    }
    
    /**
     * Whether to perform data validation on CSV content.
     * @return true to validate data, false to skip validation
     */
    public boolean isValidateData() {
        return validateData;
    }
    
    public void setValidateData(boolean validateData) {
        this.validateData = validateData;
    }
    
    @Override
    public String toString() {
        return "CsvUploadRequest{" +
                "dryRun=" + dryRun +
                ", maxInstruments=" + maxInstruments +
                ", skipDuplicates=" + skipDuplicates +
                ", validateData=" + validateData +
                '}';
    }
}
