package com.investment.api.model;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Response model for watch list performance recalculation operation.
 */
@Schema(description = "Response for watch list performance recalculation")
public class RecalculatePerformanceResponse {

    @Schema(description = "Total number of watch list items processed", example = "10")
    private int totalItems;

    @Schema(description = "Number of items successfully updated", example = "8")
    private int successfulUpdates;

    @Schema(description = "Number of items that failed to update", example = "1")
    private int failedUpdates;

    @Schema(description = "Number of items skipped due to insufficient data", example = "1")
    private int skippedItems;

    @Schema(description = "List of symbols that were successfully updated")
    private List<String> successfulSymbols;

    @Schema(description = "List of symbols that failed to update")
    private List<String> failedSymbols;

    @Schema(description = "List of symbols that were skipped")
    private List<String> skippedSymbols;

    @Schema(description = "Processing time in milliseconds", example = "2500")
    private long processingTimeMs;

    /**
     * Default constructor.
     */
    public RecalculatePerformanceResponse() {
    }

    /**
     * Constructor with all fields.
     */
    public RecalculatePerformanceResponse(int totalItems, int successfulUpdates, int failedUpdates, int skippedItems,
                                        List<String> successfulSymbols, List<String> failedSymbols, 
                                        List<String> skippedSymbols, long processingTimeMs) {
        this.totalItems = totalItems;
        this.successfulUpdates = successfulUpdates;
        this.failedUpdates = failedUpdates;
        this.skippedItems = skippedItems;
        this.successfulSymbols = successfulSymbols;
        this.failedSymbols = failedSymbols;
        this.skippedSymbols = skippedSymbols;
        this.processingTimeMs = processingTimeMs;
    }

    /**
     * Get summary message for the operation.
     */
    public String getSummaryMessage() {
        return String.format("Processed %d items: %d successful, %d failed, %d skipped (%.2fs)",
                totalItems, successfulUpdates, failedUpdates, skippedItems, processingTimeMs / 1000.0);
    }

    // Getters and Setters

    public int getTotalItems() {
        return totalItems;
    }

    public void setTotalItems(int totalItems) {
        this.totalItems = totalItems;
    }

    public int getSuccessfulUpdates() {
        return successfulUpdates;
    }

    public void setSuccessfulUpdates(int successfulUpdates) {
        this.successfulUpdates = successfulUpdates;
    }

    public int getFailedUpdates() {
        return failedUpdates;
    }

    public void setFailedUpdates(int failedUpdates) {
        this.failedUpdates = failedUpdates;
    }

    public int getSkippedItems() {
        return skippedItems;
    }

    public void setSkippedItems(int skippedItems) {
        this.skippedItems = skippedItems;
    }

    public List<String> getSuccessfulSymbols() {
        return successfulSymbols;
    }

    public void setSuccessfulSymbols(List<String> successfulSymbols) {
        this.successfulSymbols = successfulSymbols;
    }

    public List<String> getFailedSymbols() {
        return failedSymbols;
    }

    public void setFailedSymbols(List<String> failedSymbols) {
        this.failedSymbols = failedSymbols;
    }

    public List<String> getSkippedSymbols() {
        return skippedSymbols;
    }

    public void setSkippedSymbols(List<String> skippedSymbols) {
        this.skippedSymbols = skippedSymbols;
    }

    public long getProcessingTimeMs() {
        return processingTimeMs;
    }

    public void setProcessingTimeMs(long processingTimeMs) {
        this.processingTimeMs = processingTimeMs;
    }

    @Override
    public String toString() {
        return "RecalculatePerformanceResponse{" +
                "totalItems=" + totalItems +
                ", successfulUpdates=" + successfulUpdates +
                ", failedUpdates=" + failedUpdates +
                ", skippedItems=" + skippedItems +
                ", successfulSymbols=" + successfulSymbols +
                ", failedSymbols=" + failedSymbols +
                ", skippedSymbols=" + skippedSymbols +
                ", processingTimeMs=" + processingTimeMs +
                '}';
    }
}
