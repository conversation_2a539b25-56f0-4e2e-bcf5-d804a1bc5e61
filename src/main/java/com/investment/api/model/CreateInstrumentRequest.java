package com.investment.api.model;

import com.investment.model.InstrumentType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;

/**
 * Request model for creating a new financial instrument.
 */
@Schema(description = "Request parameters for creating a new financial instrument")
public class CreateInstrumentRequest {
    
    @Schema(description = "Financial instrument symbol", example = "AAPL", required = true)
    @NotBlank(message = "Symbol is required")
    @Size(max = 20, message = "Symbol must not exceed 20 characters")
    @Pattern(regexp = "^[A-Z0-9.-]+$", message = "Symbol must contain only uppercase letters, numbers, dots, and hyphens")
    private String symbol;
    
    @Schema(description = "Company or instrument name", example = "Apple Inc.", required = true)
    @NotBlank(message = "Name is required")
    @Size(max = 100, message = "Name must not exceed 100 characters")
    private String name;
    
    @Schema(description = "Type of financial instrument", example = "US_STOCK", required = true)
    @NotNull(message = "Instrument type is required")
    private InstrumentType type;
    
    @Schema(description = "Market capitalization in USD", example = "2500000000000.00")
    @DecimalMin(value = "0.0", message = "Market cap must be non-negative")
    @DecimalMax(value = "999999999999999.99", message = "Market cap is too large")
    private BigDecimal marketCap;
    
    @Schema(description = "Country of incorporation", example = "United States")
    @Size(max = 50, message = "Country must not exceed 50 characters")
    private String country;
    
    @Schema(description = "Initial Public Offering year", example = "1980")
    @Min(value = 1800, message = "IPO year must be after 1800")
    @Max(value = 2100, message = "IPO year must be before 2100")
    private Integer ipoYear;
    
    @Schema(description = "Business sector", example = "Technology")
    @Size(max = 100, message = "Sector must not exceed 100 characters")
    private String sector;
    
    @Schema(description = "Industry classification", example = "Consumer Electronics")
    @Size(max = 100, message = "Industry must not exceed 100 characters")
    private String industry;
    
    // Default constructor
    public CreateInstrumentRequest() {}
    
    // Constructor with required fields
    public CreateInstrumentRequest(String symbol, String name, InstrumentType type) {
        this.symbol = symbol;
        this.name = name;
        this.type = type;
    }
    
    // Getters and Setters
    public String getSymbol() { return symbol; }
    public void setSymbol(String symbol) { this.symbol = symbol; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public InstrumentType getType() { return type; }
    public void setType(InstrumentType type) { this.type = type; }
    
    public BigDecimal getMarketCap() { return marketCap; }
    public void setMarketCap(BigDecimal marketCap) { this.marketCap = marketCap; }
    
    public String getCountry() { return country; }
    public void setCountry(String country) { this.country = country; }
    
    public Integer getIpoYear() { return ipoYear; }
    public void setIpoYear(Integer ipoYear) { this.ipoYear = ipoYear; }
    
    public String getSector() { return sector; }
    public void setSector(String sector) { this.sector = sector; }
    
    public String getIndustry() { return industry; }
    public void setIndustry(String industry) { this.industry = industry; }
    
    /**
     * Validate the request data.
     */
    public void validate() {
        if (symbol != null) {
            symbol = symbol.trim().toUpperCase();
        }
        if (name != null) {
            name = name.trim();
        }
        if (country != null && !country.trim().isEmpty()) {
            country = country.trim();
        } else {
            country = null;
        }
        if (sector != null && !sector.trim().isEmpty()) {
            sector = sector.trim();
        } else {
            sector = null;
        }
        if (industry != null && !industry.trim().isEmpty()) {
            industry = industry.trim();
        } else {
            industry = null;
        }
    }
    
    @Override
    public String toString() {
        return String.format("CreateInstrumentRequest{symbol='%s', name='%s', type=%s, marketCap=%s, country='%s', ipoYear=%s, sector='%s', industry='%s'}",
                symbol, name, type, marketCap, country, ipoYear, sector, industry);
    }
}
