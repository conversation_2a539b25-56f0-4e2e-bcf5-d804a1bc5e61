package com.investment.api.model;

/**
 * Request model for symbol validation operations.
 */
public class ValidationRequest {
    private boolean dryRun = true;
    private boolean forceRefresh = false;
    
    public ValidationRequest() {}
    
    public ValidationRequest(boolean dryRun, boolean forceRefresh) {
        this.dryRun = dryRun;
        this.forceRefresh = forceRefresh;
    }
    
    /**
     * Whether to perform a dry run (no actual deletions).
     * @return true if this is a dry run, false for actual cleanup
     */
    public boolean isDryRun() {
        return dryRun;
    }
    
    public void setDryRun(boolean dryRun) {
        this.dryRun = dryRun;
    }
    
    /**
     * Whether to force refresh of SEC data from remote source.
     * @return true to force download, false to use cached data if available
     */
    public boolean isForceRefresh() {
        return forceRefresh;
    }
    
    public void setForceRefresh(boolean forceRefresh) {
        this.forceRefresh = forceRefresh;
    }
    
    @Override
    public String toString() {
        return "ValidationRequest{" +
                "dryRun=" + dryRun +
                ", forceRefresh=" + forceRefresh +
                '}';
    }
}
