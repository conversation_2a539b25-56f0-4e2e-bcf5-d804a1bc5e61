package com.investment.api.model;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Response model for symbol validation operations.
 */
public class ValidationResponse {
    private final int totalSymbolsInDatabase;
    private final int validSymbols;
    private final int invalidSymbols;
    private final List<String> invalidSymbolsList;
    private final int deletedSymbols;
    private final int deletedOhlcvRecords;
    private final boolean dryRun;
    private final LocalDateTime timestamp;
    private final String summary;
    
    public ValidationResponse(int totalSymbolsInDatabase, int validSymbols, int invalidSymbols,
                            List<String> invalidSymbolsList, int deletedSymbols, int deletedOhlcvRecords,
                            boolean dryRun) {
        this.totalSymbolsInDatabase = totalSymbolsInDatabase;
        this.validSymbols = validSymbols;
        this.invalidSymbols = invalidSymbols;
        this.invalidSymbolsList = invalidSymbolsList;
        this.deletedSymbols = deletedSymbols;
        this.deletedOhlcvRecords = deletedOhlcvRecords;
        this.dryRun = dryRun;
        this.timestamp = LocalDateTime.now();
        this.summary = generateSummary();
    }
    
    private String generateSummary() {
        if (dryRun) {
            return String.format("DRY RUN: Found %d invalid symbols out of %d total. " +
                    "Would delete %d instruments and %d OHLCV records.",
                    invalidSymbols, totalSymbolsInDatabase, invalidSymbols, deletedOhlcvRecords);
        } else {
            return String.format("CLEANUP COMPLETED: Deleted %d invalid symbols out of %d total. " +
                    "Removed %d instruments and %d OHLCV records.",
                    deletedSymbols, totalSymbolsInDatabase, deletedSymbols, deletedOhlcvRecords);
        }
    }
    
    public int getTotalSymbolsInDatabase() {
        return totalSymbolsInDatabase;
    }
    
    public int getValidSymbols() {
        return validSymbols;
    }
    
    public int getInvalidSymbols() {
        return invalidSymbols;
    }
    
    public List<String> getInvalidSymbolsList() {
        return invalidSymbolsList;
    }
    
    public int getDeletedSymbols() {
        return deletedSymbols;
    }
    
    public int getDeletedOhlcvRecords() {
        return deletedOhlcvRecords;
    }
    
    public boolean isDryRun() {
        return dryRun;
    }
    
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
    
    public String getSummary() {
        return summary;
    }
    
    @Override
    public String toString() {
        return "ValidationResponse{" +
                "totalSymbolsInDatabase=" + totalSymbolsInDatabase +
                ", validSymbols=" + validSymbols +
                ", invalidSymbols=" + invalidSymbols +
                ", invalidSymbolsList=" + invalidSymbolsList +
                ", deletedSymbols=" + deletedSymbols +
                ", deletedOhlcvRecords=" + deletedOhlcvRecords +
                ", dryRun=" + dryRun +
                ", timestamp=" + timestamp +
                ", summary='" + summary + '\'' +
                '}';
    }
}
