package com.investment.api.model;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Request model for updating OHLCV data for position symbols.
 */
@Schema(description = "Request for updating OHLCV data for position symbols")
public class PositionsUpdateRequest {

    @Schema(description = "Whether to perform a dry run (validation only) without actual updates",
            example = "false", defaultValue = "false")
    private boolean dryRun = false;

    @Schema(description = "Whether to recalculate P&L after OHLCV updates",
            example = "true", defaultValue = "true")
    private boolean recalculatePnL = true;

    @Schema(description = "Stop-loss calculation mode to use",
            example = "STANDARD", defaultValue = "STANDARD")
    private StopLossMode stopLossMode = StopLossMode.STANDARD;

    /**
     * Enum for stop-loss calculation modes.
     */
    public enum StopLossMode {
        @Schema(description = "Standard stop-loss calculation using basic parameters")
        STANDARD,
        @Schema(description = "Enhanced stop-loss calculation using dynamic risk management")
        ENHANCED
    }

    public PositionsUpdateRequest() {
    }

    public PositionsUpdateRequest(boolean dryRun, boolean recalculatePnL) {
        this.dryRun = dryRun;
        this.recalculatePnL = recalculatePnL;
    }

    public PositionsUpdateRequest(boolean dryRun, boolean recalculatePnL, StopLossMode stopLossMode) {
        this.dryRun = dryRun;
        this.recalculatePnL = recalculatePnL;
        this.stopLossMode = stopLossMode;
    }

    public boolean isDryRun() {
        return dryRun;
    }

    public void setDryRun(boolean dryRun) {
        this.dryRun = dryRun;
    }

    public boolean isRecalculatePnL() {
        return recalculatePnL;
    }

    public void setRecalculatePnL(boolean recalculatePnL) {
        this.recalculatePnL = recalculatePnL;
    }

    public StopLossMode getStopLossMode() {
        return stopLossMode;
    }

    public void setStopLossMode(StopLossMode stopLossMode) {
        this.stopLossMode = stopLossMode;
    }

    @Override
    public String toString() {
        return "PositionsUpdateRequest{" +
                "dryRun=" + dryRun +
                ", recalculatePnL=" + recalculatePnL +
                ", stopLossMode=" + stopLossMode +
                '}';
    }
}
