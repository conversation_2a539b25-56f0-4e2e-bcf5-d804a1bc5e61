package com.investment.api.model;

/**
 * Request model for SEC synchronization operations.
 */
public class SyncRequest {
    private boolean dryRun = true;
    private boolean forceRefresh = false;
    private int maxInstruments = 1000; // Limit to prevent overwhelming the system
    
    public SyncRequest() {}
    
    public SyncRequest(boolean dryRun, boolean forceRefresh) {
        this.dryRun = dryRun;
        this.forceRefresh = forceRefresh;
    }
    
    public SyncRequest(boolean dryRun, boolean forceRefresh, int maxInstruments) {
        this.dryRun = dryRun;
        this.forceRefresh = forceRefresh;
        this.maxInstruments = maxInstruments;
    }
    
    /**
     * Whether to perform a dry run (no actual insertions).
     * @return true if this is a dry run, false for actual sync
     */
    public boolean isDryRun() {
        return dryRun;
    }
    
    public void setDryRun(boolean dryRun) {
        this.dryRun = dryRun;
    }
    
    /**
     * Whether to force refresh of SEC data from remote source.
     * @return true to force download, false to use cached data if available
     */
    public boolean isForceRefresh() {
        return forceRefresh;
    }
    
    public void setForceRefresh(boolean forceRefresh) {
        this.forceRefresh = forceRefresh;
    }
    
    /**
     * Maximum number of instruments to add in a single operation.
     * @return maximum number of instruments to process
     */
    public int getMaxInstruments() {
        return maxInstruments;
    }
    
    public void setMaxInstruments(int maxInstruments) {
        this.maxInstruments = maxInstruments;
    }
    
    @Override
    public String toString() {
        return "SyncRequest{" +
                "dryRun=" + dryRun +
                ", forceRefresh=" + forceRefresh +
                ", maxInstruments=" + maxInstruments +
                '}';
    }
}
