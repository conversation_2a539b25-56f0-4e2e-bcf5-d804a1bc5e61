package com.investment.api.model;

import com.investment.model.Instrument;
import com.investment.model.InstrumentType;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;

/**
 * Response model for instrument data.
 */
@Schema(description = "Financial instrument information")
public class InstrumentResponse {
    
    @Schema(description = "Financial instrument symbol", example = "AAPL")
    private String symbol;
    
    @Schema(description = "Company or instrument name", example = "Apple Inc.")
    private String name;
    
    @Schema(description = "Type of financial instrument")
    private InstrumentType type;
    
    @Schema(description = "Market capitalization in USD", example = "2500000000000.00")
    private BigDecimal marketCap;
    
    @Schema(description = "Country of incorporation", example = "United States")
    private String country;
    
    @Schema(description = "Initial Public Offering year", example = "1980")
    private Integer ipoYear;
    
    @Schema(description = "Business sector", example = "Technology")
    private String sector;
    
    @Schema(description = "Industry classification", example = "Consumer Electronics")
    private String industry;
    
    // Default constructor
    public InstrumentResponse() {}
    
    // Constructor from Instrument entity
    public InstrumentResponse(Instrument instrument) {
        this.symbol = instrument.getSymbol();
        this.name = instrument.getName();
        this.type = instrument.getType();
        this.marketCap = instrument.getMarketCap();
        this.country = instrument.getCountry();
        this.ipoYear = instrument.getIpoYear();
        this.sector = instrument.getSector();
        this.industry = instrument.getIndustry();
    }
    
    // Getters and Setters
    public String getSymbol() { return symbol; }
    public void setSymbol(String symbol) { this.symbol = symbol; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public InstrumentType getType() { return type; }
    public void setType(InstrumentType type) { this.type = type; }
    
    public BigDecimal getMarketCap() { return marketCap; }
    public void setMarketCap(BigDecimal marketCap) { this.marketCap = marketCap; }
    
    public String getCountry() { return country; }
    public void setCountry(String country) { this.country = country; }
    
    public Integer getIpoYear() { return ipoYear; }
    public void setIpoYear(Integer ipoYear) { this.ipoYear = ipoYear; }
    
    public String getSector() { return sector; }
    public void setSector(String sector) { this.sector = sector; }
    
    public String getIndustry() { return industry; }
    public void setIndustry(String industry) { this.industry = industry; }
    
    @Override
    public String toString() {
        return String.format("InstrumentResponse{symbol='%s', name='%s', type=%s, marketCap=%s, country='%s', ipoYear=%s, sector='%s', industry='%s'}",
                symbol, name, type, marketCap, country, ipoYear, sector, industry);
    }
}
