package com.investment.api.model;

/**
 * Request model for refreshing OHLCV data for all instruments.
 */
public class RefreshAllRequest {
    private boolean dryRun = true;
    private int maxSymbols = 100;
    private boolean skipExisting = false;
    private int startIndex = 0;
    private Integer endIndex = null; // Optional - if not provided, use startIndex + maxSymbols

    public RefreshAllRequest() {}

    public RefreshAllRequest(boolean dryRun, int maxSymbols, boolean skipExisting) {
        this.dryRun = dryRun;
        this.maxSymbols = maxSymbols;
        this.skipExisting = skipExisting;
    }

    public RefreshAllRequest(boolean dryRun, int maxSymbols, boolean skipExisting, int startIndex, Integer endIndex) {
        this.dryRun = dryRun;
        this.maxSymbols = maxSymbols;
        this.skipExisting = skipExisting;
        this.startIndex = startIndex;
        this.endIndex = endIndex;
    }
    
    /**
     * Whether to perform a dry run (no actual data updates).
     * @return true if this is a dry run, false for actual refresh
     */
    public boolean isDryRun() {
        return dryRun;
    }
    
    public void setDryRun(boolean dryRun) {
        this.dryRun = dryRun;
    }
    
    /**
     * Maximum number of symbols to process in a single operation.
     * @return maximum number of symbols to process
     */
    public int getMaxSymbols() {
        return maxSymbols;
    }
    
    public void setMaxSymbols(int maxSymbols) {
        this.maxSymbols = maxSymbols;
    }
    
    /**
     * Whether to skip symbols that already have recent data.
     * @return true to skip symbols with recent data, false to refresh all
     */
    public boolean isSkipExisting() {
        return skipExisting;
    }
    
    public void setSkipExisting(boolean skipExisting) {
        this.skipExisting = skipExisting;
    }

    /**
     * Starting position in the ordered instrument list (0-based index).
     * @return starting index for pagination
     */
    public int getStartIndex() {
        return startIndex;
    }

    public void setStartIndex(int startIndex) {
        this.startIndex = startIndex;
    }

    /**
     * Ending position in the ordered instrument list (exclusive).
     * If not provided, uses startIndex + maxSymbols.
     * @return ending index for pagination, or null if not specified
     */
    public Integer getEndIndex() {
        return endIndex;
    }

    public void setEndIndex(Integer endIndex) {
        this.endIndex = endIndex;
    }

    /**
     * Calculate the effective limit for database queries.
     * @return the number of records to fetch
     */
    public int getEffectiveLimit() {
        if (endIndex != null) {
            return Math.min(endIndex - startIndex, maxSymbols);
        }
        return maxSymbols;
    }

    /**
     * Calculate the effective end index.
     * @return the calculated end index
     */
    public int getEffectiveEndIndex() {
        if (endIndex != null) {
            return Math.min(endIndex, startIndex + maxSymbols);
        }
        return startIndex + maxSymbols;
    }

    @Override
    public String toString() {
        return "RefreshAllRequest{" +
                "dryRun=" + dryRun +
                ", maxSymbols=" + maxSymbols +
                ", skipExisting=" + skipExisting +
                ", startIndex=" + startIndex +
                ", endIndex=" + endIndex +
                '}';
    }
}
