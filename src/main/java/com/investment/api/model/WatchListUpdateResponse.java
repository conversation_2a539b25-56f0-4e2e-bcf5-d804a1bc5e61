package com.investment.api.model;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import java.util.Map;

/**
 * Response model for watch list OHLCV data update operations.
 */
@Schema(description = "Response containing results of watch list OHLCV data update")
public class WatchListUpdateResponse {

    @Schema(description = "Total number of symbols in watch list", example = "25")
    private int totalSymbols;

    @Schema(description = "Number of symbols with successful OHLCV updates", example = "23")
    private int ohlcvSuccessCount;

    @Schema(description = "Number of symbols with OHLCV update errors", example = "2")
    private int ohlcvErrorCount;

    @Schema(description = "Number of symbols processed for Bollinger Bands", example = "23")
    private int bollingerBandsProcessed;

    @Schema(description = "Number of symbols processed for DMI indicators", example = "23")
    private int dmiProcessed;

    @Schema(description = "Total number of OHLCV records updated", example = "1250")
    private int totalRecordsUpdated;

    @Schema(description = "Total processing time in milliseconds", example = "45000")
    private long processingTimeMs;

    @Schema(description = "Detailed results for each processing phase")
    private ProcessingPhases phases;

    @Schema(description = "Summary message of the update operation")
    private String summary;

    // Default constructor
    public WatchListUpdateResponse() {
        this.phases = new ProcessingPhases();
    }

    // Constructor with basic parameters
    public WatchListUpdateResponse(int totalSymbols, int ohlcvSuccessCount, int ohlcvErrorCount) {
        this();
        this.totalSymbols = totalSymbols;
        this.ohlcvSuccessCount = ohlcvSuccessCount;
        this.ohlcvErrorCount = ohlcvErrorCount;
    }

    // Nested class for processing phases
    @Schema(description = "Detailed results for each processing phase")
    public static class ProcessingPhases {
        @Schema(description = "OHLCV update results per symbol")
        private List<SymbolUpdateResult> ohlcv;

        @Schema(description = "Bollinger Bands calculation results")
        private Map<String, Object> bollingerBands;

        @Schema(description = "DMI calculation results")
        private Map<String, Object> dmi;

        public List<SymbolUpdateResult> getOhlcv() {
            return ohlcv;
        }

        public void setOhlcv(List<SymbolUpdateResult> ohlcv) {
            this.ohlcv = ohlcv;
        }

        public Map<String, Object> getBollingerBands() {
            return bollingerBands;
        }

        public void setBollingerBands(Map<String, Object> bollingerBands) {
            this.bollingerBands = bollingerBands;
        }

        public Map<String, Object> getDmi() {
            return dmi;
        }

        public void setDmi(Map<String, Object> dmi) {
            this.dmi = dmi;
        }
    }

    // Nested class for individual symbol results
    @Schema(description = "Update result for individual symbol")
    public static class SymbolUpdateResult {
        @Schema(description = "Stock symbol", example = "AAPL")
        private String symbol;

        @Schema(description = "Update status", example = "success")
        private String status;

        @Schema(description = "Success or informational message")
        private String message;

        @Schema(description = "Error message if update failed")
        private String error;

        public SymbolUpdateResult() {}

        public SymbolUpdateResult(String symbol, String status, String message) {
            this.symbol = symbol;
            this.status = status;
            this.message = message;
        }

        public SymbolUpdateResult(String symbol, String status, String message, String error) {
            this.symbol = symbol;
            this.status = status;
            this.message = message;
            this.error = error;
        }

        // Getters and setters
        public String getSymbol() {
            return symbol;
        }

        public void setSymbol(String symbol) {
            this.symbol = symbol;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getError() {
            return error;
        }

        public void setError(String error) {
            this.error = error;
        }
    }

    // Getters and setters
    public int getTotalSymbols() {
        return totalSymbols;
    }

    public void setTotalSymbols(int totalSymbols) {
        this.totalSymbols = totalSymbols;
    }

    public int getOhlcvSuccessCount() {
        return ohlcvSuccessCount;
    }

    public void setOhlcvSuccessCount(int ohlcvSuccessCount) {
        this.ohlcvSuccessCount = ohlcvSuccessCount;
    }

    public int getOhlcvErrorCount() {
        return ohlcvErrorCount;
    }

    public void setOhlcvErrorCount(int ohlcvErrorCount) {
        this.ohlcvErrorCount = ohlcvErrorCount;
    }

    public int getBollingerBandsProcessed() {
        return bollingerBandsProcessed;
    }

    public void setBollingerBandsProcessed(int bollingerBandsProcessed) {
        this.bollingerBandsProcessed = bollingerBandsProcessed;
    }

    public int getDmiProcessed() {
        return dmiProcessed;
    }

    public void setDmiProcessed(int dmiProcessed) {
        this.dmiProcessed = dmiProcessed;
    }

    public int getTotalRecordsUpdated() {
        return totalRecordsUpdated;
    }

    public void setTotalRecordsUpdated(int totalRecordsUpdated) {
        this.totalRecordsUpdated = totalRecordsUpdated;
    }

    public long getProcessingTimeMs() {
        return processingTimeMs;
    }

    public void setProcessingTimeMs(long processingTimeMs) {
        this.processingTimeMs = processingTimeMs;
    }

    public ProcessingPhases getPhases() {
        return phases;
    }

    public void setPhases(ProcessingPhases phases) {
        this.phases = phases;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    @Override
    public String toString() {
        return "WatchListUpdateResponse{" +
                "totalSymbols=" + totalSymbols +
                ", ohlcvSuccessCount=" + ohlcvSuccessCount +
                ", ohlcvErrorCount=" + ohlcvErrorCount +
                ", bollingerBandsProcessed=" + bollingerBandsProcessed +
                ", dmiProcessed=" + dmiProcessed +
                ", totalRecordsUpdated=" + totalRecordsUpdated +
                ", processingTimeMs=" + processingTimeMs +
                ", summary='" + summary + '\'' +
                '}';
    }
}
