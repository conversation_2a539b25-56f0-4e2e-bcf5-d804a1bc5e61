package com.investment.api.model;

import com.investment.model.InstrumentType;

import java.util.List;

/**
 * Request model for updating OHLCV data.
 */
public class UpdateRequest {
    private List<String> symbols;
    private String name;
    private InstrumentType type;
    
    // Default constructor for JSON deserialization
    public UpdateRequest() {
    }
    
    public UpdateRequest(List<String> symbols) {
        this.symbols = symbols;
    }
    
    public UpdateRequest(List<String> symbols, String name, InstrumentType type) {
        this.symbols = symbols;
        this.name = name;
        this.type = type;
    }
    
    public List<String> getSymbols() {
        return symbols;
    }
    
    public void setSymbols(List<String> symbols) {
        this.symbols = symbols;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public InstrumentType getType() {
        return type;
    }
    
    public void setType(InstrumentType type) {
        this.type = type;
    }
}
