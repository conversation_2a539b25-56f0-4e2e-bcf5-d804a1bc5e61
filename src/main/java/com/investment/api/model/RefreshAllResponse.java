package com.investment.api.model;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Response model for refresh all OHLCV data operations.
 */
public class RefreshAllResponse {
    private final int totalInstruments;
    private final int processedSymbols;
    private final int skippedSymbols;
    private final int successfulUpdates;
    private final int failedUpdates;
    private final int totalDataPointsUpdated;
    private final List<String> processedSymbolsList;
    private final List<String> skippedSymbolsList;
    private final List<String> failedSymbolsList;
    private final boolean dryRun;
    private final int startIndex;
    private final int endIndex;
    private final LocalDateTime timestamp;
    private final String summary;
    
    public RefreshAllResponse(int totalInstruments, int processedSymbols, int skippedSymbols,
                            int successfulUpdates, int failedUpdates, int totalDataPointsUpdated,
                            List<String> processedSymbolsList, List<String> skippedSymbolsList,
                            List<String> failedSymbolsList, boolean dryRun, int startIndex, int endIndex) {
        this.totalInstruments = totalInstruments;
        this.processedSymbols = processedSymbols;
        this.skippedSymbols = skippedSymbols;
        this.successfulUpdates = successfulUpdates;
        this.failedUpdates = failedUpdates;
        this.totalDataPointsUpdated = totalDataPointsUpdated;
        this.processedSymbolsList = processedSymbolsList;
        this.skippedSymbolsList = skippedSymbolsList;
        this.failedSymbolsList = failedSymbolsList;
        this.dryRun = dryRun;
        this.startIndex = startIndex;
        this.endIndex = endIndex;
        this.timestamp = LocalDateTime.now();
        this.summary = generateSummary();
    }
    
    private String generateSummary() {
        String paginationInfo = String.format("(range: %d-%d)", startIndex, endIndex - 1);

        if (dryRun) {
            return String.format("DRY RUN: Would process %d out of %d instruments %s. " +
                    "Skipped %d symbols with recent data. " +
                    "Estimated %d successful updates, %d failures.",
                    processedSymbols, totalInstruments, paginationInfo, skippedSymbols,
                    successfulUpdates, failedUpdates);
        } else {
            return String.format("REFRESH COMPLETED: Processed %d out of %d instruments %s. " +
                    "Successfully updated %d symbols (%d data points), " +
                    "failed %d symbols, skipped %d symbols with recent data.",
                    processedSymbols, totalInstruments, paginationInfo, successfulUpdates,
                    totalDataPointsUpdated, failedUpdates, skippedSymbols);
        }
    }
    
    public int getTotalInstruments() {
        return totalInstruments;
    }
    
    public int getProcessedSymbols() {
        return processedSymbols;
    }
    
    public int getSkippedSymbols() {
        return skippedSymbols;
    }
    
    public int getSuccessfulUpdates() {
        return successfulUpdates;
    }
    
    public int getFailedUpdates() {
        return failedUpdates;
    }
    
    public int getTotalDataPointsUpdated() {
        return totalDataPointsUpdated;
    }
    
    public List<String> getProcessedSymbolsList() {
        return processedSymbolsList;
    }
    
    public List<String> getSkippedSymbolsList() {
        return skippedSymbolsList;
    }
    
    public List<String> getFailedSymbolsList() {
        return failedSymbolsList;
    }
    
    public boolean isDryRun() {
        return dryRun;
    }

    public int getStartIndex() {
        return startIndex;
    }

    public int getEndIndex() {
        return endIndex;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public String getSummary() {
        return summary;
    }
    
    @Override
    public String toString() {
        return "RefreshAllResponse{" +
                "totalInstruments=" + totalInstruments +
                ", processedSymbols=" + processedSymbols +
                ", skippedSymbols=" + skippedSymbols +
                ", successfulUpdates=" + successfulUpdates +
                ", failedUpdates=" + failedUpdates +
                ", totalDataPointsUpdated=" + totalDataPointsUpdated +
                ", processedSymbolsList=" + processedSymbolsList +
                ", skippedSymbolsList=" + skippedSymbolsList +
                ", failedSymbolsList=" + failedSymbolsList +
                ", dryRun=" + dryRun +
                ", startIndex=" + startIndex +
                ", endIndex=" + endIndex +
                ", timestamp=" + timestamp +
                ", summary='" + summary + '\'' +
                '}';
    }
}
