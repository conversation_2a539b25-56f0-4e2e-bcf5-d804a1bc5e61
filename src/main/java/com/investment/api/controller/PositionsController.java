package com.investment.api.controller;

import com.investment.api.model.*;
import com.investment.model.Position;
import com.investment.service.PositionsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * REST controller for portfolio position management.
 * Provides endpoints for creating, reading, updating, and deleting positions.
 */
@RestController
@RequestMapping("/api/positions")
@Tag(name = "Position Management", description = "Manage portfolio positions and track performance metrics")
public class PositionsController {
    private static final Logger logger = LoggerFactory.getLogger(PositionsController.class);
    
    private final PositionsService positionsService;
    
    public PositionsController(PositionsService positionsService) {
        this.positionsService = positionsService;
    }
    
    /**
     * Create a new position.
     */
    @PostMapping
    @Operation(
        summary = "Create a new position",
        description = "Create a new portfolio position for a financial instrument. " +
                     "The position will be created with OPEN status and initial trade data."
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "201", 
            description = "Position created successfully"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400", 
            description = "Invalid request parameters"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500", 
            description = "Internal server error"
        )
    })
    public ResponseEntity<ApiResponse<PositionResponse>> createPosition(
            @Valid @RequestBody CreatePositionRequest request) {
        
        try {
            logger.info("Creating new position: {}", request);
            
            Position position = positionsService.createPosition(request);
            PositionResponse response = new PositionResponse(position);
            
            logger.info("Created position with ID: {}", position.getId());
            
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("Position created successfully", response));
            
        } catch (IllegalArgumentException e) {
            logger.error("Invalid request for creating position: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Invalid request: " + e.getMessage()));
        } catch (SQLException e) {
            logger.error("Database error creating position", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to create position: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("Unexpected error creating position", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Unexpected error: " + e.getMessage()));
        }
    }
    
    /**
     * Get all positions with optional filtering.
     */
    @GetMapping
    @Operation(
        summary = "Get all positions",
        description = "Retrieve all portfolio positions with optional filtering by symbol, status, or side."
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "Positions retrieved successfully"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500", 
            description = "Internal server error"
        )
    })
    public ResponseEntity<ApiResponse<List<PositionResponse>>> getPositions(
            @Parameter(description = "Filter by symbol")
            @RequestParam(required = false) String symbol,
            @Parameter(description = "Filter by status")
            @RequestParam(required = false) Position.Status status,
            @Parameter(description = "Filter by side")
            @RequestParam(required = false) Position.Side side) {
        
        try {
            logger.debug("Retrieving positions with filters - symbol: {}, status: {}, side: {}", symbol, status, side);
            
            List<Position> positions = positionsService.getPositions(symbol, status, side);
            List<PositionResponse> response = positions.stream()
                    .map(PositionResponse::new)
                    .collect(Collectors.toList());
            
            String message = String.format("Retrieved %d positions", response.size());
            return ResponseEntity.ok(ApiResponse.success(message, response));
            
        } catch (SQLException e) {
            logger.error("Database error retrieving positions", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to retrieve positions: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("Unexpected error retrieving positions", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Unexpected error: " + e.getMessage()));
        }
    }
    
    /**
     * Get a specific position by ID.
     */
    @GetMapping("/{id}")
    @Operation(
        summary = "Get position by ID",
        description = "Retrieve detailed information about a specific position."
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "Position found and retrieved successfully"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "404", 
            description = "Position not found"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500", 
            description = "Internal server error"
        )
    })
    public ResponseEntity<ApiResponse<PositionResponse>> getPosition(
            @Parameter(description = "Position ID", example = "1")
            @PathVariable Long id) {
        
        try {
            logger.debug("Retrieving position by ID: {}", id);
            
            Optional<Position> position = positionsService.getPositionById(id);
            
            if (position.isPresent()) {
                PositionResponse response = new PositionResponse(position.get());
                return ResponseEntity.ok(ApiResponse.success("Position found", response));
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error("Position not found: " + id));
            }
            
        } catch (SQLException e) {
            logger.error("Database error retrieving position: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to retrieve position: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("Unexpected error retrieving position: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Unexpected error: " + e.getMessage()));
        }
    }
    
    /**
     * Update a position.
     */
    @PutMapping("/{id}")
    @Operation(
        summary = "Update a position",
        description = "Update an existing position with new market data, risk parameters, or status."
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "Position updated successfully"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400", 
            description = "Invalid request parameters"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "404", 
            description = "Position not found"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500", 
            description = "Internal server error"
        )
    })
    public ResponseEntity<ApiResponse<PositionResponse>> updatePosition(
            @Parameter(description = "Position ID", example = "1")
            @PathVariable Long id,
            @Valid @RequestBody UpdatePositionRequest request) {
        
        try {
            logger.info("Updating position ID {}: {}", id, request);
            
            if (!request.hasUpdates()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("No update fields provided"));
            }
            
            Position position = positionsService.updatePosition(id, request);
            PositionResponse response = new PositionResponse(position);
            
            logger.info("Updated position ID: {}", id);
            
            return ResponseEntity.ok(ApiResponse.success("Position updated successfully", response));
            
        } catch (IllegalArgumentException e) {
            logger.error("Invalid request for updating position {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Invalid request: " + e.getMessage()));
        } catch (SQLException e) {
            logger.error("Database error updating position: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to update position: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("Unexpected error updating position: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Unexpected error: " + e.getMessage()));
        }
    }
    
    /**
     * Update position with current market price.
     */
    @PutMapping("/{id}/price")
    @Operation(
        summary = "Update position price",
        description = "Update a position with the current market price and recalculate all metrics."
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "Position price updated successfully"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400", 
            description = "Invalid price value"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "404", 
            description = "Position not found"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500", 
            description = "Internal server error"
        )
    })
    public ResponseEntity<ApiResponse<PositionResponse>> updatePositionPrice(
            @Parameter(description = "Position ID", example = "1")
            @PathVariable Long id,
            @Parameter(description = "Current market price", example = "155.75")
            @RequestParam BigDecimal price) {
        
        try {
            logger.debug("Updating position ID {} with price: {}", id, price);
            
            if (price == null || price.compareTo(BigDecimal.ZERO) <= 0) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("Price must be positive"));
            }
            
            Position position = positionsService.updatePositionPrice(id, price);
            PositionResponse response = new PositionResponse(position);
            
            return ResponseEntity.ok(ApiResponse.success("Position price updated successfully", response));
            
        } catch (IllegalArgumentException e) {
            logger.error("Position not found for price update {}: {}", id, e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Position not found: " + id));
        } catch (SQLException e) {
            logger.error("Database error updating position price: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to update position price: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("Unexpected error updating position price: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Unexpected error: " + e.getMessage()));
        }
    }

    /**
     * Update close price for a position with automatic P&L calculation.
     */
    @PutMapping("/{id}/close-price")
    @Operation(
        summary = "Update close price",
        description = "Update the close price for a position and automatically calculate P&L metrics."
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Close price updated successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid close price"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Position not found"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<PositionResponse>> updateClosePrice(
            @Parameter(description = "Position ID", example = "1")
            @PathVariable Long id,
            @Parameter(description = "Close price", example = "155.75")
            @RequestParam BigDecimal closePrice) {

        try {
            logger.info("Updating close price for position ID {} with price: {}", id, closePrice);

            if (closePrice == null || closePrice.compareTo(BigDecimal.ZERO) <= 0) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("Close price must be positive"));
            }

            Position position = positionsService.updateClosePrice(id, closePrice);
            PositionResponse response = new PositionResponse(position);

            logger.info("Updated close price for position ID: {} with P&L: value={}, percent={}",
                       id, position.getPnlValue(), position.getPnlPercent());

            return ResponseEntity.ok(ApiResponse.success("Close price updated successfully with P&L calculation", response));

        } catch (IllegalArgumentException e) {
            logger.error("Position not found or invalid close price for ID {}: {}", id, e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Position not found: " + id));
        } catch (SQLException e) {
            logger.error("Database error updating close price for position: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to update close price: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("Unexpected error updating close price for position: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Unexpected error: " + e.getMessage()));
        }
    }

    /**
     * Update OHLCV data for all position symbols and recalculate P&L.
     */
    @PostMapping("/update-ohlcv-data")
    @Operation(
        summary = "Update OHLCV data for position symbols",
        description = "Updates OHLCV historical data for all symbols in positions and recalculates P&L using latest market prices. " +
                     "This is a comprehensive update operation that includes: " +
                     "1) OHLCV data updates from Yahoo Finance for all position symbols " +
                     "2) P&L recalculation using latest market prices from updated OHLCV data. " +
                     "The operation may take several minutes depending on the number of symbols."
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "OHLCV data update completed successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid request"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<PositionsUpdateResponse>> updateOHLCVDataForPositions(
            @RequestBody(required = false) PositionsUpdateRequest request) {

        if (request == null) {
            request = new PositionsUpdateRequest(false, true); // Default: not dry run, recalculate P&L
        }

        try {
            logger.info("Starting OHLCV data update for position symbols: {}", request);

            // Log warning for actual update operations
            if (!request.isDryRun()) {
                logger.warn("PERFORMING ACTUAL OHLCV UPDATE FOR POSITIONS - This will download data from external APIs and may take significant time");
            }

            PositionsUpdateResponse response = positionsService.updateOHLCVDataForPositions(request);

            String operation = request.isDryRun() ? "OHLCV update validation" : "OHLCV update";
            logger.info("{} completed for positions: {}", operation, response.getSummary());

            return ResponseEntity.ok(ApiResponse.success(
                    operation + " completed for positions",
                    response));

        } catch (IllegalArgumentException e) {
            logger.warn("Invalid request for positions OHLCV update: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Invalid request: " + e.getMessage()));
        } catch (SQLException e) {
            logger.error("Database error during positions OHLCV update", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Database error: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("Unexpected error during positions OHLCV update", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Unexpected error: " + e.getMessage()));
        }
    }

    /**
     * Recalculate P&L for all positions using status-aware logic.
     * OPEN positions: Updated with latest market prices from OHLCV data.
     * CLOSED positions: P&L recalculated using their own close_price field.
     */
    @PostMapping("/recalculate-pnl")
    @Operation(
        summary = "Recalculate P&L for all positions (status-aware)",
        description = "Recalculates P&L metrics for all positions using status-aware logic: " +
                     "OPEN positions are updated with latest market prices from OHLCV data, " +
                     "while CLOSED positions use their own close_price field for final P&L calculation. " +
                     "Supports both Standard and Enhanced stop-loss calculation modes."
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "P&L recalculation completed successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<Map<String, Object>>> recalculatePnLForAllPositions(
            @RequestBody(required = false) PnLRecalculationRequest request) {

        if (request == null) {
            request = new PnLRecalculationRequest(PnLRecalculationRequest.StopLossMode.STANDARD);
        }

        try {
            logger.info("Starting status-aware P&L recalculation for all positions with stop-loss mode: {}", request.getStopLossMode());

            // Convert request enum to service enum
            PositionsUpdateRequest.StopLossMode serviceStopLossMode =
                request.getStopLossMode() == PnLRecalculationRequest.StopLossMode.ENHANCED
                    ? PositionsUpdateRequest.StopLossMode.ENHANCED
                    : PositionsUpdateRequest.StopLossMode.STANDARD;

            int updatedCount = positionsService.recalculatePnLForAllPositions(serviceStopLossMode);

            Map<String, Object> result = new HashMap<>();
            result.put("updatedCount", updatedCount);
            result.put("stopLossMode", request.getStopLossMode().name());
            result.put("message", String.format("Successfully recalculated P&L for %d positions using status-aware logic with %s stop-loss mode",
                                               updatedCount, request.getStopLossMode().name()));

            logger.info("Status-aware P&L recalculation completed: updated {} positions with {} stop-loss mode",
                       updatedCount, request.getStopLossMode());

            return ResponseEntity.ok(ApiResponse.success(
                    "P&L recalculation completed successfully",
                    result));

        } catch (SQLException e) {
            logger.error("Database error during P&L recalculation", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Database error: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("Unexpected error during P&L recalculation", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Unexpected error: " + e.getMessage()));
        }
    }

    /**
     * Recalculate P&L for OPEN positions only using latest market prices.
     * This endpoint maintains backward compatibility for systems that only want to update OPEN positions.
     */
    @PostMapping("/recalculate-pnl-open-only")
    @Operation(
        summary = "Recalculate P&L for OPEN positions only",
        description = "Recalculates P&L metrics for OPEN positions only using the latest market prices from OHLCV data. " +
                     "CLOSED positions are not affected by this operation."
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "P&L recalculation for OPEN positions completed successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<Map<String, Object>>> recalculatePnLForOpenPositionsOnly() {

        try {
            logger.info("Starting P&L recalculation for OPEN positions only");

            int updatedCount = positionsService.recalculatePnLForOpenPositionsOnly();

            Map<String, Object> result = new HashMap<>();
            result.put("updatedCount", updatedCount);
            result.put("message", String.format("Successfully recalculated P&L for %d OPEN positions", updatedCount));

            logger.info("P&L recalculation for OPEN positions completed: updated {} positions", updatedCount);

            return ResponseEntity.ok(ApiResponse.success(
                    "P&L recalculation for OPEN positions completed successfully",
                    result));

        } catch (SQLException e) {
            logger.error("Database error during P&L recalculation for OPEN positions", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Database error: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("Unexpected error during P&L recalculation for OPEN positions", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Unexpected error: " + e.getMessage()));
        }
    }

    /**
     * Close a position.
     */
    @PostMapping("/{id}/close")
    @Operation(
        summary = "Close a position",
        description = "Close an open position, setting its status to CLOSED."
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "Position closed successfully"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "404", 
            description = "Position not found"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500", 
            description = "Internal server error"
        )
    })
    public ResponseEntity<ApiResponse<PositionResponse>> closePosition(
            @Parameter(description = "Position ID", example = "1")
            @PathVariable Long id) {
        
        try {
            logger.info("Closing position ID: {}", id);
            
            Position position = positionsService.closePosition(id);
            PositionResponse response = new PositionResponse(position);
            
            logger.info("Closed position ID: {}", id);
            
            return ResponseEntity.ok(ApiResponse.success("Position closed successfully", response));
            
        } catch (IllegalArgumentException e) {
            logger.error("Position not found for closing {}: {}", id, e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Position not found: " + id));
        } catch (SQLException e) {
            logger.error("Database error closing position: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to close position: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("Unexpected error closing position: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Unexpected error: " + e.getMessage()));
        }
    }
    
    /**
     * Delete a position.
     */
    @DeleteMapping("/{id}")
    @Operation(
        summary = "Delete a position",
        description = "Permanently delete a position from the database."
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "Position deleted successfully"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "404", 
            description = "Position not found"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500", 
            description = "Internal server error"
        )
    })
    public ResponseEntity<ApiResponse<String>> deletePosition(
            @Parameter(description = "Position ID", example = "1")
            @PathVariable Long id) {
        
        try {
            logger.info("Deleting position ID: {}", id);
            
            boolean deleted = positionsService.deletePosition(id);
            
            if (deleted) {
                logger.info("Deleted position ID: {}", id);
                return ResponseEntity.ok(ApiResponse.success("Position deleted successfully", "Position " + id + " deleted"));
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error("Position not found: " + id));
            }
            
        } catch (SQLException e) {
            logger.error("Database error deleting position: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to delete position: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("Unexpected error deleting position: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Unexpected error: " + e.getMessage()));
        }
    }
    
    /**
     * Get open positions only.
     */
    @GetMapping("/open")
    @Operation(
        summary = "Get open positions",
        description = "Retrieve all positions with OPEN status."
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "Open positions retrieved successfully"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500", 
            description = "Internal server error"
        )
    })
    public ResponseEntity<ApiResponse<List<PositionResponse>>> getOpenPositions() {
        try {
            logger.debug("Retrieving open positions");
            
            List<Position> positions = positionsService.getOpenPositions();
            List<PositionResponse> response = positions.stream()
                    .map(PositionResponse::new)
                    .collect(Collectors.toList());
            
            String message = String.format("Retrieved %d open positions", response.size());
            return ResponseEntity.ok(ApiResponse.success(message, response));
            
        } catch (SQLException e) {
            logger.error("Database error retrieving open positions", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to retrieve open positions: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("Unexpected error retrieving open positions", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Unexpected error: " + e.getMessage()));
        }
    }
    
    /**
     * Get positions that should be stopped out.
     */
    @GetMapping("/stop-out")
    @Operation(
        summary = "Get positions to stop out",
        description = "Retrieve positions that should be stopped out based on current prices and stop loss rules."
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "Stop out positions retrieved successfully"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500", 
            description = "Internal server error"
        )
    })
    public ResponseEntity<ApiResponse<List<PositionResponse>>> getPositionsToStopOut() {
        try {
            logger.debug("Retrieving positions that should be stopped out");
            
            List<Position> positions = positionsService.getPositionsToStopOut();
            List<PositionResponse> response = positions.stream()
                    .map(PositionResponse::new)
                    .collect(Collectors.toList());
            
            String message = String.format("Found %d positions that should be stopped out", response.size());
            return ResponseEntity.ok(ApiResponse.success(message, response));
            
        } catch (SQLException e) {
            logger.error("Database error retrieving stop out positions", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to retrieve stop out positions: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("Unexpected error retrieving stop out positions", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Unexpected error: " + e.getMessage()));
        }
    }
}
