package com.investment.database;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.time.LocalDate;

/**
 * Utility class to fix positions with null open_date values.
 * This addresses the issue where positions created before the open_date field
 * was properly implemented have null values, causing RiskManagementService
 * validation failures.
 */
public class PositionOpenDateFix {
    
    private static final Logger logger = LoggerFactory.getLogger(PositionOpenDateFix.class);
    
    private final DatabaseManager databaseManager;
    
    public PositionOpenDateFix(DatabaseManager databaseManager) {
        this.databaseManager = databaseManager;
    }
    
    /**
     * Fix all positions with null open_date by setting them to their created_date.
     * For positions where created_date is also null, use the current date.
     * 
     * @return Number of positions updated
     * @throws SQLException if database operation fails
     */
    public int fixNullOpenDates() throws SQLException {
        logger.info("Starting fix for positions with null open_date");
        
        // First, identify positions with null open_date
        String selectSql = """
            SELECT id, symbol, created_date, open_date 
            FROM positions 
            WHERE open_date IS NULL
            """;
        
        int updatedCount = 0;
        
        try (Statement stmt = databaseManager.getConnection().createStatement();
             ResultSet rs = stmt.executeQuery(selectSql)) {
            
            while (rs.next()) {
                Long positionId = rs.getLong("id");
                String symbol = rs.getString("symbol");
                java.sql.Timestamp createdDate = rs.getTimestamp("created_date");
                
                // Determine the open_date to use
                LocalDate openDate;
                if (createdDate != null) {
                    // Use the date part of created_date
                    openDate = createdDate.toLocalDateTime().toLocalDate();
                    logger.info("Position {} ({}): Using created_date {} as open_date", 
                               positionId, symbol, openDate);
                } else {
                    // Fallback to current date if created_date is also null
                    openDate = LocalDate.now();
                    logger.warn("Position {} ({}): created_date is null, using current date {} as open_date", 
                               positionId, symbol, openDate);
                }
                
                // Update the position
                updatePositionOpenDate(positionId, openDate);
                updatedCount++;
            }
        }
        
        logger.info("Fixed {} positions with null open_date", updatedCount);
        return updatedCount;
    }
    
    /**
     * Update a specific position's open_date.
     */
    private void updatePositionOpenDate(Long positionId, LocalDate openDate) throws SQLException {
        String updateSql = """
            UPDATE positions 
            SET open_date = ?, updated_date = CURRENT_TIMESTAMP 
            WHERE id = ?
            """;
        
        try (PreparedStatement pstmt = databaseManager.getConnection().prepareStatement(updateSql)) {
            pstmt.setDate(1, java.sql.Date.valueOf(openDate));
            pstmt.setLong(2, positionId);
            
            int rowsUpdated = pstmt.executeUpdate();
            if (rowsUpdated == 1) {
                logger.debug("Successfully updated position {} with open_date {}", positionId, openDate);
            } else {
                logger.error("Failed to update position {} - {} rows affected", positionId, rowsUpdated);
            }
        }
    }
    
    /**
     * Validate that all positions now have non-null open_date values.
     * 
     * @return true if all positions have open_date, false otherwise
     */
    public boolean validateAllPositionsHaveOpenDate() throws SQLException {
        String sql = "SELECT COUNT(*) as null_count FROM positions WHERE open_date IS NULL";
        
        try (Statement stmt = databaseManager.getConnection().createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            if (rs.next()) {
                int nullCount = rs.getInt("null_count");
                if (nullCount == 0) {
                    logger.info("Validation passed: All positions have non-null open_date");
                    return true;
                } else {
                    logger.error("Validation failed: {} positions still have null open_date", nullCount);
                    return false;
                }
            }
        }
        
        return false;
    }
    
    /**
     * Main method for running the fix as a standalone utility.
     */
    public static void main(String[] args) {
        try {
            DatabaseManager dbManager = new DatabaseManager();
            dbManager.initDatabase();
            
            PositionOpenDateFix fix = new PositionOpenDateFix(dbManager);
            
            // Run the fix
            int updatedCount = fix.fixNullOpenDates();
            System.out.printf("Updated %d positions with null open_date%n", updatedCount);
            
            // Validate the fix
            boolean allValid = fix.validateAllPositionsHaveOpenDate();
            System.out.printf("Validation result: %s%n", allValid ? "PASSED" : "FAILED");
            
            dbManager.closeConnection();
            
        } catch (Exception e) {
            System.err.println("Error running position open_date fix: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
