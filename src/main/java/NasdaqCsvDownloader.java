import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.CookieHandler;
import java.net.CookieManager;
import java.net.HttpURLConnection;
import java.net.URL;

public class NasdaqCsvDownloader {
  private static final String SCREENER_URL = "https://www.nasdaq.com/market-activity/stocks/screener";
  private static final String DOWNLOAD_URL = "https://www.nasdaq.com/api/v1/screener/download";
  private static final String OUTPUT_FILE = "nasdaq_screener_data.csv";

  public static void main(String[] args) {
    try {
      downloadCsv();
    } catch (IOException e) {
      System.err.println("Error downloading CSV: " + e.getMessage());
      e.printStackTrace();
    }
  }

  public static void downloadCsv() throws IOException {
    // Step 1: Create a CookieManager to handle cookies
    CookieManager cookieManager = new CookieManager();
    CookieHandler.setDefault(cookieManager);

    // Step 2: Visit the screener page to capture cookies
    visitScreenerPage();

    // Step 3: Download the CSV file
    HttpURLConnection downloadConnection = setupConnection(DOWNLOAD_URL);
    int statusCode = downloadConnection.getResponseCode();

    if (statusCode == HttpURLConnection.HTTP_OK) {
      // Step 4: Save the CSV file
      saveCsvFile(downloadConnection);
      System.out.println("CSV file downloaded successfully.");
    } else {
      System.out.println("Failed to download CSV. Status code: " + statusCode);
    }

    downloadConnection.disconnect();
  }

  private static void visitScreenerPage() throws IOException {
    HttpURLConnection connection = setupConnection(SCREENER_URL);
    // Just need to make the request to capture cookies; no need to read the response
    connection.connect();
    connection.disconnect();
  }

  private static HttpURLConnection setupConnection(String urlString) throws IOException {
    URL url = new URL(urlString);
    HttpURLConnection connection = (HttpURLConnection) url.openConnection();

    // Set headers to mimic a browser
    connection.setRequestMethod("GET");
    connection.setRequestProperty("User-Agent",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
    connection.setRequestProperty("Referer", SCREENER_URL);

    // Allow redirects
    connection.setInstanceFollowRedirects(true);
    return connection;
  }

  private static void saveCsvFile(HttpURLConnection connection) throws IOException {
    try (InputStream inputStream = connection.getInputStream();
        FileOutputStream outputStream = new FileOutputStream(OUTPUT_FILE)) {
      byte[] buffer = new byte[1024];
      int bytesRead;
      while ((bytesRead = inputStream.read(buffer)) != -1) {
        outputStream.write(buffer, 0, bytesRead);
      }
    }
  }
}