// API Response wrapper
export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
  timestamp: string;
}

// Paginated Response wrapper
export interface PaginatedResponse<T> {
  content: T[];
  page: number;
  size: number;
  totalElements: number;
  totalPages: number;
  first: boolean;
  last: boolean;
  empty: boolean;
  numberOfElements: number;
}

// Financial Instrument
export interface Instrument {
  symbol: string;
  name: string;
  type: string; // InstrumentType enum as string
  marketCap?: number;
  country?: string;
  ipoYear?: number;
  sector?: string;
  industry?: string;
}

// Create Instrument Request
export interface CreateInstrumentRequest {
  symbol: string;
  name: string;
  type: string; // InstrumentType enum as string
  marketCap?: number;
  country?: string;
  ipoYear?: number;
  sector?: string;
  industry?: string;
}

// OHLCV Data
export interface OHLCV {
  symbol: string;
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  // Technical Indicators
  bollingerMiddle?: number;
  bollingerStdDev?: number;
  bollingerUpper?: number;
  bollingerLower?: number;
  dmiPlusDi?: number;
  dmiMinusDi?: number;
  dmiDx?: number;
  dmiAdx?: number;
}

// Position
export interface Position {
  id: number;
  symbol: string;
  position: number; // Quantity of shares/units
  side: 'BUY' | 'SELL';
  status: 'OPEN' | 'CLOSED';
  tradePrice: number;
  tradeValue: number;
  initPortfolioNetValue?: number;
  lastPrice?: number;
  lastValue?: number;
  riskUnit?: number;
  stopPercent?: number;
  highestAfterTrade?: number;
  stopValueFromHighest?: number;
  lastBbmb?: number;
  bbmbAdjPercent?: number;
  stopValueFromBbmb?: number;
  expandOrContract?: 'EXPANDING' | 'CONTRACTING';
  effectiveStopValue?: number;
  pnlValue?: number;
  pnlPercent?: number;
  closePrice?: number;
  openDate?: string; // ISO date string
  closeDate?: string; // ISO date string
  aggressiveStopPercent?: number;
  conservativeStopPercent?: number;
  riskMode?: 'AGGRESSIVE' | 'CONSERVATIVE';
  conservativePeriodEndDate?: string; // ISO datetime string
  createdDate: string;
  updatedDate: string;
}

// Position Request Types
export interface CreatePositionRequest {
  symbol: string;
  position: number;
  side: 'BUY' | 'SELL';
  tradePrice: number;
  initPortfolioNetValue?: number;
  riskUnit?: number;
  stopPercent?: number;
  bbmbAdjPercent?: number;
  openDate?: string; // ISO date string (YYYY-MM-DD)
  aggressiveStopPercent?: number;
  conservativeStopPercent?: number;
}

export interface UpdatePositionRequest {
  position?: number; // Quantity of shares/units (for future backend support)
  tradePrice?: number; // Entry price (for future backend support)
  lastPrice?: number;
  riskUnit?: number;
  stopPercent?: number;
  bbmbAdjPercent?: number;
  expandOrContract?: 'EXPANDING' | 'CONTRACTING';
  status?: 'OPEN' | 'CLOSED';
  closePrice?: number;
  openDate?: string; // ISO date string (YYYY-MM-DD)
  closeDate?: string; // ISO date string (YYYY-MM-DD)
  aggressiveStopPercent?: number;
  conservativeStopPercent?: number;
}

// Watch List Item
export interface WatchListItem {
  id: number;
  displayIndex: number;
  symbol: string;
  startDate: string; // LocalDate as ISO string
  remarks?: string;
  oneMonthPerf?: number;
  threeMonthPerf?: number;
  sixMonthPerf?: number;
  bullishBbStreak?: number;
  dmiBullishStreak?: number;
  combinedSignalStreak?: number;
  createdDate: string; // LocalDateTime as ISO string
  updatedDate: string; // LocalDateTime as ISO string
}

// Watch List Request Types
export interface CreateWatchListRequest {
  displayIndex: number;
  symbol: string;
  startDate: string; // ISO date string
  remarks?: string;
}

export interface UpdateWatchListRequest {
  displayIndex?: number;
  remarks?: string;
  oneMonthPerf?: number;
  threeMonthPerf?: number;
  sixMonthPerf?: number;
}

export interface ReorderWatchListRequest {
  idToIndexMap: Record<number, number>;
}

export interface RecalculatePerformanceResponse {
  totalItems: number;
  successfulUpdates: number;
  failedUpdates: number;
  skippedItems: number;
  successfulSymbols: string[];
  failedSymbols: string[];
  skippedSymbols: string[];
  processingTimeMs: number;
  summaryMessage?: string;
}

// Process Information
export interface ProcessInfo {
  processId: string;
  processType: string;
  status: string;
  startTime: string;
  endTime?: string;
  duration?: number;
  progress?: number;
  message?: string;
  metadata?: Record<string, any>;
}

// Technical Indicator Calculation Request
export interface TechnicalIndicatorRequest {
  period?: number;
  stdDevMultiplier?: number;
  minDataPoints?: number;
  startIndex?: number;
  endIndex?: number;
  maxSymbols?: number;
  symbols?: string[];
  dryRun?: boolean;
  calculationMode?: 'INCREMENTAL' | 'FULL_RECALCULATION' | 'SKIP_EXISTING';
  calculationMethod?: 'PURE_JAVA' | 'HYBRID_SQL_JAVA';
}

// DMI Calculation Progress
export interface DMICalculationProgress {
  isCalculating: boolean;
  startTime?: Date;
  elapsedSeconds: number;
  currentOperation?: string;
  processedSymbols?: number;
  totalSymbols?: number;
  progressPercentage?: number;
}

// DMI Calculation Result
export interface DMICalculationResult {
  success: boolean;
  message: string;
  elapsedTimeMs: number;
  processedSymbols: number;
  totalRecordsUpdated: number;
  symbolsWithInsufficientData?: string[];
  errors?: string[];
}

// Refresh Request
export interface RefreshAllRequest {
  dryRun: boolean;
  maxSymbols?: number;
  skipExisting: boolean;
  startIndex?: number;
  endIndex?: number;
}

// Validation Request
export interface ValidationRequest {
  dryRun: boolean;
  maxSymbols?: number;
  removeInvalidSymbols: boolean;
  validateMarketCap: boolean;
  minMarketCap?: number;
}

// CSV Upload Response
export interface CsvUploadResponse {
  totalRows: number;
  processedRows: number;
  skippedRows: number;
  errorRows: number;
  validationErrors: string[];
  summary: string;
}

// Sync Response
export interface SyncResponse {
  totalSecInstruments: number;
  newInstruments: number;
  existingInstruments: number;
  errors: number;
  summary: string;
}

// Chart Data Point
export interface ChartDataPoint {
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  bollingerUpper?: number;
  bollingerMiddle?: number;
  bollingerLower?: number;
}

// Bulk Operation Interfaces
export interface BulkUpdateRequest {
  symbols: string[];
  startDate?: string;
  endDate?: string;
  dryRun?: boolean;
}

export interface BulkUpdateProgress {
  symbol: string;
  status: 'pending' | 'processing' | 'success' | 'error';
  message?: string;
  error?: string;
}

export interface BulkUpdateResult {
  totalSymbols: number;
  successCount: number;
  errorCount: number;
  results: BulkUpdateProgress[];
  summary: string;
}

// Watch List OHLCV Update Interfaces
export interface WatchListUpdateRequest {
  recalculateTechnicalIndicators?: boolean;
  dryRun?: boolean;
}

export interface WatchListUpdateProgress {
  phase: 'ohlcv' | 'bollinger' | 'dmi' | 'complete';
  phaseDescription: string;
  symbol?: string;
  status: 'pending' | 'processing' | 'success' | 'error';
  message?: string;
  error?: string;
  currentStep: number;
  totalSteps: number;
}

export interface WatchListUpdateResult {
  totalSymbols: number;
  ohlcvSuccessCount: number;
  ohlcvErrorCount: number;
  bollingerBandsProcessed: number;
  dmiProcessed: number;
  totalRecordsUpdated: number;
  processingTimeMs: number;
  phases: {
    ohlcv: BulkUpdateProgress[];
    bollingerBands?: any;
    dmi?: any;
  };
  summary: string;
}

// Positions OHLCV Update Interfaces
export interface PositionsUpdateRequest {
  recalculatePnL?: boolean;
  dryRun?: boolean;
  stopLossMode?: 'STANDARD' | 'ENHANCED';
}

export interface PnLRecalculationRequest {
  stopLossMode?: 'STANDARD' | 'ENHANCED';
}

export interface PositionsUpdateResult {
  totalSymbols: number;
  ohlcvSuccessCount: number;
  ohlcvErrorCount: number;
  totalRecordsUpdated: number;
  pnlUpdatedCount: number;
  totalPositions: number;
  processingTimeMs: number;
  ohlcvResults: {
    symbol: string;
    status: string;
    message?: string;
    error?: string;
  }[];
  summary: string;
}
