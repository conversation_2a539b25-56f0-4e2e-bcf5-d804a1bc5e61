import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Skeleton,
  Tooltip,
  TablePagination,
  useTheme,
  useMediaQuery,
  Fab,
  Snackbar,
  Autocomplete,
  CircularProgress,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  DragHandle as DragHandleIcon,
  Refresh as RefreshIcon,
  Calculate as CalculateIcon,
  Update as UpdateIcon,
} from '@mui/icons-material';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
// Removed date-fns dependency - using native JavaScript date formatting
import { WatchListService } from '../services/api/watchListService';
import { InstrumentService } from '../services/api/instrumentService';
import WatchListUpdateProgressDialog from '../components/WatchListUpdateProgressDialog';
import {
  WatchListItem,
  CreateWatchListRequest,
  UpdateWatchListRequest,
  Instrument,
  ApiResponse,
  WatchListUpdateResult,
} from '../types/api';

// Sortable Row Component for drag-and-drop
interface SortableRowProps {
  item: WatchListItem;
  isMobile: boolean;
  onEdit: (item: WatchListItem) => void;
  onDelete: (item: WatchListItem) => void;
}

const SortableRow: React.FC<SortableRowProps> = ({ item, isMobile, onEdit, onDelete }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: item.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const formatPerformance = (value: number | null | undefined) => {
    if (value === null || value === undefined) return 'N/A';
    const formatted = (value * 100).toFixed(2);
    return `${formatted}%`;
  };

  const getPerformanceColor = (value: number | null | undefined) => {
    if (value === null || value === undefined) return 'text.secondary';
    return value >= 0 ? 'success.main' : 'error.main';
  };

  const getPerformanceIcon = (value: number | null | undefined) => {
    if (value === null || value === undefined) return null;
    return value >= 0 ? <TrendingUpIcon fontSize="small" /> : <TrendingDownIcon fontSize="small" />;
  };

  return (
    <TableRow
      ref={setNodeRef}
      style={style}
      hover
      sx={{
        cursor: isDragging ? 'grabbing' : 'default',
        '&:hover .drag-handle': {
          color: 'primary.main',
        },
      }}
    >
      <TableCell>
        <Box display="flex" alignItems="center">
          <DragHandleIcon
            className="drag-handle"
            color="disabled"
            sx={{ mr: 1, cursor: 'grab' }}
            {...attributes}
            {...listeners}
          />
          {item.displayIndex}
        </Box>
      </TableCell>
      <TableCell>
        <Typography variant="body2" fontWeight="medium">
          {item.symbol}
        </Typography>
      </TableCell>
      <TableCell>{item.startDate}</TableCell>
      <TableCell>
        {item.remarks ? (
          <Typography variant="body2" noWrap title={item.remarks}>
            {item.remarks.length > 30 ? `${item.remarks.substring(0, 30)}...` : item.remarks}
          </Typography>
        ) : (
          <Typography variant="body2" color="text.secondary">
            No remarks
          </Typography>
        )}
      </TableCell>
      {!isMobile && (
        <>
          <TableCell align="right">
            <Box display="flex" alignItems="center" justifyContent="flex-end" gap={0.5}>
              {getPerformanceIcon(item.oneMonthPerf)}
              <Typography
                variant="body2"
                color={getPerformanceColor(item.oneMonthPerf)}
                fontWeight="medium"
              >
                {formatPerformance(item.oneMonthPerf)}
              </Typography>
            </Box>
          </TableCell>
          <TableCell align="right">
            <Box display="flex" alignItems="center" justifyContent="flex-end" gap={0.5}>
              {getPerformanceIcon(item.threeMonthPerf)}
              <Typography
                variant="body2"
                color={getPerformanceColor(item.threeMonthPerf)}
                fontWeight="medium"
              >
                {formatPerformance(item.threeMonthPerf)}
              </Typography>
            </Box>
          </TableCell>
          <TableCell align="right">
            <Box display="flex" alignItems="center" justifyContent="flex-end" gap={0.5}>
              {getPerformanceIcon(item.sixMonthPerf)}
              <Typography
                variant="body2"
                color={getPerformanceColor(item.sixMonthPerf)}
                fontWeight="medium"
              >
                {formatPerformance(item.sixMonthPerf)}
              </Typography>
            </Box>
          </TableCell>
          <TableCell align="center">
            <Chip
              label={item.bullishBbStreak || 0}
              size="small"
              color={item.bullishBbStreak && item.bullishBbStreak > 0 ? 'success' : 'default'}
              variant={item.bullishBbStreak && item.bullishBbStreak > 0 ? 'filled' : 'outlined'}
            />
          </TableCell>
          <TableCell align="center">
            <Chip
              label={item.dmiBullishStreak || 0}
              size="small"
              color={item.dmiBullishStreak && item.dmiBullishStreak > 0 ? 'info' : 'default'}
              variant={item.dmiBullishStreak && item.dmiBullishStreak > 0 ? 'filled' : 'outlined'}
            />
          </TableCell>
          <TableCell align="center">
            <Chip
              label={item.combinedSignalStreak || 0}
              size="small"
              color={item.combinedSignalStreak && item.combinedSignalStreak > 0 ? 'warning' : 'default'}
              variant={item.combinedSignalStreak && item.combinedSignalStreak > 0 ? 'filled' : 'outlined'}
            />
          </TableCell>
        </>
      )}
      <TableCell align="center">
        <Box display="flex" gap={0.5} justifyContent="center">
          <Tooltip title="Edit">
            <IconButton
              size="small"
              onClick={() => onEdit(item)}
              color="primary"
            >
              <EditIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Delete">
            <IconButton
              size="small"
              color="error"
              onClick={() => onDelete(item)}
            >
              <DeleteIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </TableCell>
    </TableRow>
  );
};

const WatchList: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Component mount tracking to prevent state updates after unmount
  const isMountedRef = useRef(true);

  // Track instruments loaded state to avoid dependency issues
  const instrumentsLoadedOnceRef = useRef(false);

  // Request cancellation and deduplication
  const loadInstrumentsAbortControllerRef = useRef<AbortController | null>(null);
  const isLoadingInstrumentsRef = useRef(false);

  // Debug tracking
  const debugCounterRef = useRef(0);

  // Loading timeout safeguard
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // State management
  const [watchListItems, setWatchListItems] = useState<WatchListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [recalculating, setRecalculating] = useState(false);
  const [reordering, setReordering] = useState(false);

  // OHLCV Update states
  const [updatingOHLCV, setUpdatingOHLCV] = useState(false);
  const [showUpdateDialog, setShowUpdateDialog] = useState(false);
  const [showUpdateConfirmation, setShowUpdateConfirmation] = useState(false);
  const [updateResult, setUpdateResult] = useState<WatchListUpdateResult | null>(null);
  const [updateStartTime, setUpdateStartTime] = useState<Date | null>(null);

  // Pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);

  // Dialog states
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<WatchListItem | null>(null);

  // Form states
  const [formData, setFormData] = useState({
    symbol: '',
    startDate: new Date().toISOString().split('T')[0], // yyyy-MM-dd format
    remarks: '',
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [submitting, setSubmitting] = useState(false);

  // Available instruments for symbol selection
  const [availableInstruments, setAvailableInstruments] = useState<Instrument[]>([]);
  const [instrumentsLoading, setInstrumentsLoading] = useState(false);
  const [instrumentsLoadedOnce, setInstrumentsLoadedOnce] = useState(false);
  const [instrumentsRetryCount, setInstrumentsRetryCount] = useState(0);

  // Snackbar state
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info' | 'warning';
  }>({
    open: false,
    message: '',
    severity: 'info',
  });

  // Load watch list items
  const loadWatchListItems = useCallback(async (showRefreshing = false) => {
    try {
      if (showRefreshing) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);

      const response = await WatchListService.getWatchListItems();

      if (response.success && response.data) {
        // Sort by display index
        const sortedItems = response.data.sort((a, b) => a.displayIndex - b.displayIndex);
        setWatchListItems(sortedItems);
      } else {
        setError(response.message || 'Failed to load watch list items');
      }
    } catch (err: any) {
      console.error('Error loading watch list items:', err);
      setError(err.message || 'Failed to load watch list items');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  // Helper function to show snackbar messages
  const showSnackbar = useCallback((message: string, severity: 'success' | 'error' | 'info' | 'warning') => {
    setSnackbar({ open: true, message, severity });
  }, []);

  // Load available instruments for symbol selection
  const loadAvailableInstruments = useCallback(async (forceRefresh: boolean = false, retryAttempt: number = 0) => {
    const debugId = ++debugCounterRef.current;
    console.log(`🔄 [${debugId}] loadAvailableInstruments called:`, {
      forceRefresh,
      retryAttempt,
      isMounted: isMountedRef.current,
      isAlreadyLoading: isLoadingInstrumentsRef.current,
      instrumentsLoadedOnce: instrumentsLoadedOnceRef.current,
      availableInstrumentsCount: availableInstruments.length,
      instrumentsLoading
    });

    // Check if component is still mounted before starting
    if (!isMountedRef.current) {
      console.log(`❌ [${debugId}] Component unmounted, skipping instrument load`);
      return;
    }

    // Prevent duplicate requests
    if (isLoadingInstrumentsRef.current && !forceRefresh) {
      console.log(`⚠️ [${debugId}] Already loading instruments, skipping duplicate request`);
      return;
    }

    // Cancel any existing request
    if (loadInstrumentsAbortControllerRef.current) {
      console.log(`🛑 [${debugId}] Cancelling previous request`);
      loadInstrumentsAbortControllerRef.current.abort();
    }

    // Create new abort controller
    const abortController = new AbortController();
    loadInstrumentsAbortControllerRef.current = abortController;

    try {
      isLoadingInstrumentsRef.current = true;
      setInstrumentsLoading(true);
      console.log(`📡 [${debugId}] Starting API request to /instruments`);

      // Set up loading timeout safeguard (30 seconds)
      loadingTimeoutRef.current = setTimeout(() => {
        console.error(`⏰ [${debugId}] Loading timeout reached (30s), forcing loading state to false`);
        if (isMountedRef.current) {
          setInstrumentsLoading(false);
          isLoadingInstrumentsRef.current = false;
          showSnackbar('Loading instruments timed out. Please try refreshing.', 'warning');
        }
      }, 30000);

      // Use maximum allowed page size (1000) to load as many instruments as possible
      // Server constraint: page size must be between 1 and 1000
      const response = await InstrumentService.getInstruments(0, 1000, 'marketCap', 'desc');

      console.log(`📥 [${debugId}] API response received:`, {
        success: response.success,
        dataLength: response.data?.content?.length || 0,
        message: response.message
      });

      // Check if component is still mounted before updating state
      if (!isMountedRef.current) {
        console.log(`❌ [${debugId}] Component unmounted during instrument load, skipping state update`);
        return;
      }

      // Check if request was aborted
      if (abortController.signal.aborted) {
        console.log(`🛑 [${debugId}] Request was aborted, skipping state update`);
        return;
      }

      if (response.success && response.data) {
        console.log(`✅ [${debugId}] Setting instruments data:`, {
          instrumentsCount: response.data.content.length,
          previousCount: availableInstruments.length
        });

        setAvailableInstruments(response.data.content);
        setInstrumentsLoadedOnce(true);
        instrumentsLoadedOnceRef.current = true;
        setInstrumentsRetryCount(0);

        // Cache the instruments data
        try {
          localStorage.setItem('watchlist_instruments_cache', JSON.stringify({
            data: response.data.content,
            timestamp: Date.now()
          }));
          console.log(`💾 [${debugId}] Instruments cached successfully`);
        } catch (error) {
          console.error(`❌ [${debugId}] Error caching instruments:`, error);
        }

        if (forceRefresh) {
          showSnackbar(`Refreshed ${response.data.content.length} instruments for symbol selection`, 'success');
        }

        console.log(`✅ [${debugId}] Successfully loaded ${response.data.content.length} instruments for Watch List symbol selection`);
      } else {
        console.log(`❌ [${debugId}] API response was not successful:`, response);

        // Only show error message if this is a forced refresh or we haven't loaded instruments before
        if (forceRefresh || !instrumentsLoadedOnceRef.current) {
          const errorMessage = response.message || 'Failed to load instruments for symbol selection';
          console.log(`🚨 [${debugId}] Showing error message: ${errorMessage}`);
          showSnackbar(errorMessage, 'error');
        }

        // Attempt retry for non-forced refresh if we haven't loaded instruments before
        if (!forceRefresh && !instrumentsLoadedOnceRef.current && retryAttempt < 2 && isMountedRef.current) {
          console.log(`🔄 [${debugId}] Scheduling retry (attempt ${retryAttempt + 1})`);
          setInstrumentsRetryCount(retryAttempt + 1);
          setTimeout(() => {
            if (isMountedRef.current) {
              console.log(`🔄 [${debugId}] Executing retry (attempt ${retryAttempt + 1})`);
              loadAvailableInstruments(false, retryAttempt + 1);
            } else {
              console.log(`❌ [${debugId}] Component unmounted, skipping retry`);
            }
          }, 1000 * (retryAttempt + 1)); // Exponential backoff
        }
      }
    } catch (err: any) {
      console.error(`❌ [${debugId}] Error loading instruments:`, err);

      // Check if this is an abort error
      if (err.name === 'AbortError') {
        console.log(`🛑 [${debugId}] Request was aborted`);
        return;
      }

      // Check if component is still mounted before updating state
      if (!isMountedRef.current) {
        console.log(`❌ [${debugId}] Component unmounted during instrument load error, skipping state update`);
        return;
      }

      // Enhanced error handling for specific API errors
      let errorMessage = 'Failed to load instruments for symbol selection';
      if (err.message) {
        if (err.message.includes('Page size must be between')) {
          errorMessage = 'Invalid page size parameter. Please contact support.';
          console.error(`🚨 [${debugId}] Page size validation error - this should not happen after fix`);
        } else if (err.message.includes('400')) {
          errorMessage = 'Invalid request parameters. Please try refreshing the page.';
        } else if (err.message.includes('500')) {
          errorMessage = 'Server error. Please try again later.';
        } else {
          errorMessage = err.message;
        }
      }

      // Only show error message if this is a forced refresh or we haven't loaded instruments before
      if (forceRefresh || !instrumentsLoadedOnceRef.current) {
        console.log(`🚨 [${debugId}] Showing error message: ${errorMessage}`);
        showSnackbar(errorMessage, 'error');
      }

      // Attempt retry for non-forced refresh if we haven't loaded instruments before
      if (!forceRefresh && !instrumentsLoadedOnceRef.current && retryAttempt < 2 && isMountedRef.current) {
        console.log(`🔄 [${debugId}] Scheduling retry after error (attempt ${retryAttempt + 1})`);
        setInstrumentsRetryCount(retryAttempt + 1);
        setTimeout(() => {
          if (isMountedRef.current) {
            console.log(`🔄 [${debugId}] Executing retry after error (attempt ${retryAttempt + 1})`);
            loadAvailableInstruments(false, retryAttempt + 1);
          } else {
            console.log(`❌ [${debugId}] Component unmounted, skipping retry after error`);
          }
        }, 1000 * (retryAttempt + 1)); // Exponential backoff
      }
    } finally {
      console.log(`🏁 [${debugId}] Finally block - cleaning up:`, {
        isMounted: isMountedRef.current,
        wasAborted: abortController.signal.aborted
      });

      // Clear loading timeout
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
        loadingTimeoutRef.current = null;
        console.log(`🏁 [${debugId}] Cleared loading timeout`);
      }

      isLoadingInstrumentsRef.current = false;

      if (isMountedRef.current && !abortController.signal.aborted) {
        setInstrumentsLoading(false);
        console.log(`🏁 [${debugId}] Set instrumentsLoading to false`);
      } else {
        console.log(`🏁 [${debugId}] Skipped setting instrumentsLoading to false (unmounted or aborted)`);
      }

      // Clear the abort controller if it's the current one
      if (loadInstrumentsAbortControllerRef.current === abortController) {
        loadInstrumentsAbortControllerRef.current = null;
        console.log(`🏁 [${debugId}] Cleared abort controller`);
      }
    }
  }, [showSnackbar]);

  // Handle manual symbol search when user types a symbol not in the list
  const handleSymbolInputChange = useCallback(async (event: any, value: string, reason: string) => {
    if (reason === 'input' && value && value.length >= 2) {
      const searchTerm = value.toUpperCase();
      // Check if the search term looks like a symbol and isn't already in our list
      if (/^[A-Z0-9.-]+$/.test(searchTerm) && !availableInstruments.some(inst => inst.symbol.includes(searchTerm))) {
        try {
          console.log(`Searching for symbol: ${searchTerm}`);
          const response = await InstrumentService.searchInstruments(searchTerm);
          if (response.success && response.data && response.data.length > 0) {
            // Add any new instruments found to our list
            setAvailableInstruments(prev => {
              const newInstruments = response.data.filter(newInst =>
                !prev.some(existingInst => existingInst.symbol === newInst.symbol)
              );
              if (newInstruments.length > 0) {
                console.log(`Found ${newInstruments.length} new instruments matching "${searchTerm}"`);
                return [...prev, ...newInstruments];
              }
              return prev;
            });
          }
        } catch (error) {
          console.error('Error searching for symbols:', error);
        }
      }
    }
  }, [availableInstruments]);

  // Search for a specific symbol if it's not in the current instruments list
  const searchForMissingSymbol = useCallback(async (symbol: string) => {
    console.log(`Searching for missing symbol: ${symbol}`);
    try {
      // First try to search by exact symbol match
      const response = await InstrumentService.searchInstruments(symbol);
      console.log(`Search response for ${symbol}:`, response);

      if (response.success && response.data && response.data.length > 0) {
        // Look for exact match first
        let foundInstrument = response.data.find(inst => inst.symbol === symbol);

        // If no exact match, look for case-insensitive match
        if (!foundInstrument) {
          foundInstrument = response.data.find(inst => inst.symbol.toUpperCase() === symbol.toUpperCase());
        }

        if (foundInstrument) {
          console.log(`Found instrument:`, foundInstrument);
          // Add the found instrument to our list
          setAvailableInstruments(prev => {
            const exists = prev.some(inst => inst.symbol === foundInstrument!.symbol);
            if (!exists) {
              console.log(`Adding ${foundInstrument!.symbol} to available instruments list`);
              return [...prev, foundInstrument!];
            }
            console.log(`${foundInstrument!.symbol} already exists in available instruments list`);
            return prev;
          });
          showSnackbar(`Found symbol: ${foundInstrument.symbol}`, 'success');
        } else {
          console.log(`No exact match found for ${symbol} in search results`);
          showSnackbar(`Symbol ${symbol} not found in database`, 'warning');
        }
      } else {
        console.log(`No search results for ${symbol}`);
        showSnackbar(`Symbol ${symbol} not found in database`, 'warning');
      }
    } catch (error: any) {
      console.error('Error searching for symbol:', error);
      showSnackbar(`Error searching for symbol ${symbol}: ${error.message || 'Unknown error'}`, 'error');
    }
  }, [showSnackbar]);

  // Load cached instruments on component mount
  useEffect(() => {
    console.log('🗄️ Cache loading effect triggered');
    const cachedInstruments = localStorage.getItem('watchlist_instruments_cache');
    if (cachedInstruments) {
      try {
        const parsed = JSON.parse(cachedInstruments);
        const cacheAge = Date.now() - parsed.timestamp;
        const cacheValid = parsed.data && parsed.timestamp && cacheAge < 5 * 60 * 1000; // 5 minutes cache

        console.log('🗄️ Cache analysis:', {
          hasCachedData: !!parsed.data,
          cacheAgeMs: cacheAge,
          cacheAgeMinutes: Math.round(cacheAge / 60000),
          isValid: cacheValid,
          instrumentsCount: parsed.data?.length || 0
        });

        if (cacheValid) {
          console.log('✅ Loading instruments from cache:', parsed.data.length, 'instruments');
          setAvailableInstruments(parsed.data);
          setInstrumentsLoadedOnce(true);
          instrumentsLoadedOnceRef.current = true;
        } else {
          console.log('❌ Cache is invalid or expired, removing');
          localStorage.removeItem('watchlist_instruments_cache');
        }
      } catch (error) {
        console.error('❌ Error parsing cached instruments:', error);
        localStorage.removeItem('watchlist_instruments_cache');
      }
    } else {
      console.log('🗄️ No cached instruments found');
    }
  }, []);

  // Initial load - only load watch list items, not instruments
  useEffect(() => {
    console.log('🚀 Initial load effect triggered - loading watch list items only');
    loadWatchListItems();
  }, [loadWatchListItems]);

  // Check for refresh flag when component mounts or when coming from other pages
  useEffect(() => {
    const shouldRefresh = localStorage.getItem('refreshInstruments');
    console.log('🔄 Refresh flag effect triggered:', { shouldRefresh });

    if (shouldRefresh === 'true') {
      localStorage.removeItem('refreshInstruments');
      console.log('🔄 Refresh flag found - will refresh instruments when dialog opens');
      // Clear the cache so instruments will be reloaded when dialog opens
      localStorage.removeItem('watchlist_instruments_cache');
      instrumentsLoadedOnceRef.current = false;
      setInstrumentsLoadedOnce(false);
    }
  }, []);

  // Cleanup effect to prevent state updates after unmount
  useEffect(() => {
    console.log('🏗️ Component mounted, setting up cleanup');
    return () => {
      console.log('🧹 Component unmounting, cleaning up');
      isMountedRef.current = false;

      // Cancel any ongoing requests
      if (loadInstrumentsAbortControllerRef.current) {
        console.log('🛑 Cancelling ongoing request during unmount');
        loadInstrumentsAbortControllerRef.current.abort();
        loadInstrumentsAbortControllerRef.current = null;
      }

      // Clear loading timeout
      if (loadingTimeoutRef.current) {
        console.log('⏰ Clearing loading timeout during unmount');
        clearTimeout(loadingTimeoutRef.current);
        loadingTimeoutRef.current = null;
      }

      isLoadingInstrumentsRef.current = false;
    };
  }, []);

  // Load instruments when add dialog opens
  useEffect(() => {
    if (addDialogOpen && !instrumentsLoadedOnceRef.current && !isLoadingInstrumentsRef.current) {
      console.log('🚪 Dialog opened, loading instruments from cache or API');

      // Try to load from cache first
      const cachedInstruments = localStorage.getItem('watchlist_instruments_cache');
      if (cachedInstruments) {
        try {
          const parsed = JSON.parse(cachedInstruments);
          const cacheAge = Date.now() - parsed.timestamp;
          const cacheValid = parsed.data && parsed.timestamp && cacheAge < 5 * 60 * 1000; // 5 minutes cache

          if (cacheValid) {
            console.log('🚪 Loading instruments from cache for dialog:', parsed.data.length, 'instruments');
            setAvailableInstruments(parsed.data);
            setInstrumentsLoadedOnce(true);
            instrumentsLoadedOnceRef.current = true;
            return;
          }
        } catch (error) {
          console.error('🚪 Error parsing cached instruments for dialog:', error);
        }
      }

      // If no valid cache, load from API
      console.log('🚪 No valid cache, loading instruments from API for dialog');
      loadAvailableInstruments();
    }
  }, [addDialogOpen, loadAvailableInstruments]);

  // Debug effect to track instrumentsLoading state changes
  useEffect(() => {
    console.log('📊 instrumentsLoading state changed:', {
      instrumentsLoading,
      instrumentsLoadedOnce,
      availableInstrumentsCount: availableInstruments.length,
      isLoadingInstruments: isLoadingInstrumentsRef.current,
      timestamp: new Date().toISOString()
    });
  }, [instrumentsLoading, instrumentsLoadedOnce, availableInstruments.length]);

  // Form validation
  const validateForm = (isEdit = false) => {
    const errors: Record<string, string> = {};

    if (!isEdit && !formData.symbol.trim()) {
      errors.symbol = 'Symbol is required';
    }

    if (!formData.startDate) {
      errors.startDate = 'Start date is required';
    } else {
      const startDate = new Date(formData.startDate);
      const today = new Date();
      if (startDate > today) {
        errors.startDate = 'Start date cannot be in the future';
      }
    }

    if (formData.remarks && formData.remarks.length > 128) {
      errors.remarks = 'Remarks cannot exceed 128 characters';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      symbol: '',
      startDate: new Date().toISOString().split('T')[0], // yyyy-MM-dd format
      remarks: '',
    });
    setFormErrors({});
    setSelectedItem(null);
  };

  // Handle opening the add dialog - load instruments when dialog opens
  const handleOpenAddDialog = () => {
    console.log('🚪 Opening add dialog');
    setAddDialogOpen(true);

    // Load instruments when dialog opens if not already loaded
    if (!instrumentsLoadedOnceRef.current && !isLoadingInstrumentsRef.current) {
      console.log('🚪 Loading instruments for dialog');
      loadAvailableInstruments();
    } else if (instrumentsLoadedOnceRef.current) {
      console.log('🚪 Instruments already loaded, using cached data');
    } else {
      console.log('🚪 Instruments already loading, waiting for completion');
    }
  };

  // Handle add symbol
  const handleAddSymbol = async () => {
    if (!validateForm()) return;

    try {
      setSubmitting(true);

      // Calculate next display index
      const nextDisplayIndex = watchListItems.length > 0
        ? Math.max(...watchListItems.map(item => item.displayIndex)) + 1
        : 1;

      const request: CreateWatchListRequest = {
        displayIndex: nextDisplayIndex,
        symbol: formData.symbol.toUpperCase(),
        startDate: formData.startDate,
        remarks: formData.remarks.trim() || undefined,
      };

      const response = await WatchListService.createWatchListItem(request);

      if (response.success) {
        await loadWatchListItems();
        setAddDialogOpen(false);
        resetForm();
        setSnackbar({
          open: true,
          message: `Successfully added ${formData.symbol.toUpperCase()} to watch list`,
          severity: 'success',
        });
      } else {
        setError(response.message || 'Failed to add symbol to watch list');
      }
    } catch (err: any) {
      console.error('Error adding symbol:', err);
      setError(err.message || 'Failed to add symbol to watch list');
    } finally {
      setSubmitting(false);
    }
  };

  // Validate reorder mapping
  const validateReorderMapping = (idToIndexMap: { [key: number]: number }, items: WatchListItem[]): boolean => {
    const expectedIds = items.map(item => item.id);
    const mappedIds = Object.keys(idToIndexMap).map(Number);
    const mappedIndexes = Object.values(idToIndexMap);

    // Check if all IDs are present
    if (expectedIds.length !== mappedIds.length) {
      console.error('ID count mismatch:', { expected: expectedIds.length, mapped: mappedIds.length });
      return false;
    }

    // Check if all expected IDs are in the mapping
    for (let i = 0; i < expectedIds.length; i++) {
      const id = expectedIds[i];
      if (!mappedIds.includes(id)) {
        console.error('Missing ID in mapping:', id);
        return false;
      }
    }

    // Check if indexes are sequential starting from 1
    const sortedIndexes = mappedIndexes.slice().sort((a, b) => a - b);
    for (let i = 0; i < sortedIndexes.length; i++) {
      if (sortedIndexes[i] !== i + 1) {
        console.error('Non-sequential indexes:', sortedIndexes);
        return false;
      }
    }

    // Check for duplicate indexes using traditional approach
    for (let i = 0; i < mappedIndexes.length; i++) {
      for (let j = i + 1; j < mappedIndexes.length; j++) {
        if (mappedIndexes[i] === mappedIndexes[j]) {
          console.error('Duplicate index found:', mappedIndexes[i], 'at positions', i, 'and', j);
          return false;
        }
      }
    }

    return true;
  };

  // Handle drag end for reordering
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over || active.id === over.id) {
      return;
    }

    // Find the items in the FULL list, not just the paginated slice
    const oldIndex = watchListItems.findIndex((item) => item.id === active.id);
    const newIndex = watchListItems.findIndex((item) => item.id === over.id);

    if (oldIndex === -1 || newIndex === -1) {
      console.error('Could not find items for reordering:', { activeId: active.id, overId: over.id });
      return;
    }

    // Check if both items are on the current page (for better UX feedback)
    const currentPageItems = watchListItems.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);
    const activeInCurrentPage = currentPageItems.some(item => item.id === active.id);
    const overInCurrentPage = currentPageItems.some(item => item.id === over.id);

    if (!activeInCurrentPage || !overInCurrentPage) {
      setSnackbar({
        open: true,
        message: 'Cross-page reordering is not supported. Please navigate to the same page for both items.',
        severity: 'warning',
      });
      return;
    }

    console.log(`Reordering: ${watchListItems[oldIndex].symbol} (index ${oldIndex}) -> ${watchListItems[newIndex].symbol} (index ${newIndex})`);


    // Store original items for potential rollback
    const originalItems = [...watchListItems];

    // Optimistically update the UI with the full list
    const newItems = arrayMove(watchListItems, oldIndex, newIndex);

    // Update display indexes for ALL items in the correct order
    const updatedItems = newItems.map((item, index) => ({
      ...item,
      displayIndex: index + 1, // Display index starts from 1
    }));

    setWatchListItems(updatedItems);

    try {
      setReordering(true);

      // Create the reorder mapping for ALL items with their new display indexes
      const idToIndexMap: { [key: number]: number } = {};
      updatedItems.forEach((item) => {
        idToIndexMap[item.id] = item.displayIndex;
      });

      // Validate the mapping before sending to backend
      if (!validateReorderMapping(idToIndexMap, updatedItems)) {
        console.error('Invalid reorder mapping, reverting changes');
        setWatchListItems(originalItems);
        setSnackbar({
          open: true,
          message: 'Invalid reorder mapping detected. Please try again.',
          severity: 'error',
        });
        return;
      }

      console.log('Reordering items with validated mapping:', idToIndexMap);

      const response = await WatchListService.reorderWatchListItems({ idToIndexMap });

      if (response.success) {
        // Verify persistence by reloading data from backend
        try {
          await loadWatchListItems();
          const movedItem = watchListItems[oldIndex];
          setSnackbar({
            open: true,
            message: `Successfully moved ${movedItem.symbol} to position ${newIndex + 1}`,
            severity: 'success',
          });

          console.log('Reorder successful and verified from backend');
        } catch (reloadErr: any) {
          console.error('Error verifying reorder persistence:', reloadErr);
          // Keep the optimistic update since the API call succeeded
          setSnackbar({
            open: true,
            message: 'Items reordered successfully (verification pending)',
            severity: 'warning',
          });
        }
      } else {
        // Revert the optimistic update
        console.error('Reorder failed:', response.message);
        setWatchListItems(originalItems);
        setSnackbar({
          open: true,
          message: response.message || 'Failed to reorder watch list items',
          severity: 'error',
        });
      }
    } catch (err: any) {
      console.error('Error reordering items:', err);
      // Revert the optimistic update
      setWatchListItems(originalItems);
      setSnackbar({
        open: true,
        message: err.message || 'Failed to reorder watch list items',
        severity: 'error',
      });
    } finally {
      setReordering(false);
    }
  };

  // Handle edit symbol
  const handleEditSymbol = async () => {
    if (!selectedItem || !validateForm(true)) return;

    try {
      setSubmitting(true);

      const request: UpdateWatchListRequest = {
        remarks: formData.remarks.trim() || undefined,
      };

      const response = await WatchListService.updateWatchListItem(selectedItem.id, request);

      if (response.success) {
        await loadWatchListItems();
        setEditDialogOpen(false);
        resetForm();
        setSnackbar({
          open: true,
          message: 'Watch list item updated successfully',
          severity: 'success',
        });
      } else {
        setError(response.message || 'Failed to update watch list item');
      }
    } catch (err: any) {
      console.error('Error updating symbol:', err);
      setError(err.message || 'Failed to update watch list item');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle delete symbol
  const handleDeleteSymbol = async () => {
    if (!selectedItem) return;

    try {
      setSubmitting(true);

      const response = await WatchListService.deleteWatchListItem(selectedItem.id);

      if (response.success) {
        await loadWatchListItems();
        setDeleteDialogOpen(false);
        resetForm();
        setSnackbar({
          open: true,
          message: `Successfully deleted ${selectedItem.symbol} from watch list`,
          severity: 'success',
        });
      } else {
        setError(response.message || 'Failed to delete watch list item');
      }
    } catch (err: any) {
      console.error('Error deleting symbol:', err);
      setError(err.message || 'Failed to delete watch list item');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle edit button click
  const handleEditClick = (item: WatchListItem) => {
    setSelectedItem(item);
    setFormData({
      symbol: item.symbol,
      startDate: item.startDate,
      remarks: item.remarks || '',
    });
    setEditDialogOpen(true);
  };

  // Handle delete button click
  const handleDeleteClick = (item: WatchListItem) => {
    setSelectedItem(item);
    setDeleteDialogOpen(true);
  };

  // Handle recalculate performance
  const handleRecalculatePerformance = async () => {
    try {
      setRecalculating(true);
      setError(null);

      const response = await WatchListService.recalculatePerformance();

      if (response.success && response.data) {
        // Refresh the watch list to show updated performance metrics
        await loadWatchListItems();

        // Show success message with details
        const result = response.data;
        const successMessage = `Performance recalculation completed: ${result.successfulUpdates} updated, ${result.failedUpdates} failed, ${result.skippedItems} skipped (${(result.processingTimeMs / 1000).toFixed(1)}s)`;

        setSnackbar({
          open: true,
          message: successMessage,
          severity: 'success',
        });

        // Clear any existing error
        setError(null);
      } else {
        setSnackbar({
          open: true,
          message: response.message || 'Failed to recalculate performance metrics',
          severity: 'error',
        });
      }
    } catch (err: any) {
      console.error('Error recalculating performance:', err);
      setSnackbar({
        open: true,
        message: err.message || 'Failed to recalculate performance metrics',
        severity: 'error',
      });
    } finally {
      setRecalculating(false);
    }
  };

  // Handle recalculate technical signals
  const handleRecalculateTechnicalSignals = async () => {
    try {
      setRecalculating(true);
      setError(null);

      const response = await WatchListService.recalculateTechnicalSignals();

      if (response.success && response.data) {
        setSnackbar({
          open: true,
          message: `Technical signals updated for ${response.data.successfulUpdates}/${response.data.totalItems} symbols`,
          severity: 'success',
        });

        // Reload the watch list to show updated technical signals
        await loadWatchListItems();

        // Clear any existing error
        setError(null);
      } else {
        setSnackbar({
          open: true,
          message: response.message || 'Failed to recalculate technical signals',
          severity: 'error',
        });
      }
    } catch (err: any) {
      console.error('Error recalculating technical signals:', err);
      setSnackbar({
        open: true,
        message: err.message || 'Failed to recalculate technical signals',
        severity: 'error',
      });
    } finally {
      setRecalculating(false);
    }
  };

  // Handle OHLCV data update
  const handleUpdateOHLCVData = () => {
    if (watchListItems.length === 0) {
      setError('No symbols in watch list to update');
      return;
    }
    setShowUpdateConfirmation(true);
  };

  const confirmUpdateOHLCVData = async () => {
    try {
      setShowUpdateConfirmation(false);
      setUpdatingOHLCV(true);
      setShowUpdateDialog(true);
      setUpdateStartTime(new Date());
      setError(null);
      setUpdateResult(null);

      const response = await WatchListService.updateOHLCVDataForWatchList({
        recalculateTechnicalIndicators: true,
        dryRun: false
      });

      if (response.success && response.data) {
        setUpdateResult(response.data);

        // Show success message
        const message = response.data.summary || 'OHLCV data update completed successfully';
        setSnackbar({
          open: true,
          message: message,
          severity: response.data.ohlcvErrorCount === 0 ? 'success' : 'warning',
        });

        // Reload the watch list to show any updated data
        await loadWatchListItems();
      } else {
        setError(response.message || 'Failed to update OHLCV data');
        setUpdateResult({
          totalSymbols: watchListItems.length,
          ohlcvSuccessCount: 0,
          ohlcvErrorCount: watchListItems.length,
          bollingerBandsProcessed: 0,
          dmiProcessed: 0,
          totalRecordsUpdated: 0,
          processingTimeMs: 0,
          phases: { ohlcv: [] },
          summary: response.message || 'Update failed'
        });
      }
    } catch (err: any) {
      console.error('Error updating OHLCV data:', err);
      setError(err.message || 'Failed to update OHLCV data');
      setUpdateResult({
        totalSymbols: watchListItems.length,
        ohlcvSuccessCount: 0,
        ohlcvErrorCount: watchListItems.length,
        bollingerBandsProcessed: 0,
        dmiProcessed: 0,
        totalRecordsUpdated: 0,
        processingTimeMs: 0,
        phases: { ohlcv: [] },
        summary: err.message || 'Update failed due to network error'
      });
    } finally {
      setUpdatingOHLCV(false);
    }
  };

  const handleCloseUpdateDialog = () => {
    setShowUpdateDialog(false);
    setUpdateResult(null);
    setUpdateStartTime(null);
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" gutterBottom>
          Watch List
        </Typography>
        <Box display="flex" gap={1}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => loadWatchListItems(true)}
            disabled={loading || refreshing || recalculating || updatingOHLCV || reordering}
          >
            Refresh
          </Button>
          <Button
            variant="outlined"
            startIcon={<UpdateIcon />}
            onClick={handleUpdateOHLCVData}
            disabled={loading || refreshing || recalculating || updatingOHLCV || reordering || watchListItems.length === 0}
            color="secondary"
          >
            {updatingOHLCV ? 'Updating...' : 'Update OHLCV Data'}
          </Button>
          <Button
            variant="outlined"
            startIcon={<CalculateIcon />}
            onClick={handleRecalculatePerformance}
            disabled={loading || refreshing || recalculating || updatingOHLCV || reordering || watchListItems.length === 0}
          >
            {recalculating ? 'Recalculating...' : 'Recalculate Performance'}
          </Button>
          <Button
            variant="outlined"
            startIcon={<CalculateIcon />}
            onClick={handleRecalculateTechnicalSignals}
            disabled={loading || refreshing || recalculating || updatingOHLCV || reordering || watchListItems.length === 0}
            color="info"
          >
            {recalculating ? 'Recalculating...' : 'Recalculate Signals'}
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleOpenAddDialog}
            disabled={loading || recalculating || updatingOHLCV || reordering}
          >
            Add Symbol
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Watch List Table */}
      <Paper sx={{ position: 'relative' }}>
        {reordering && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(255, 255, 255, 0.7)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 1000,
            }}
          >
            <Box display="flex" alignItems="center" gap={2}>
              <CircularProgress size={24} />
              <Typography variant="body2">Reordering items...</Typography>
            </Box>
          </Box>
        )}
        <TableContainer>
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell><strong>Order</strong></TableCell>
                  <TableCell><strong>Symbol</strong></TableCell>
                  <TableCell><strong>Start Date</strong></TableCell>
                  <TableCell><strong>Remarks</strong></TableCell>
                  {!isMobile && (
                    <>
                      <TableCell align="right"><strong>1M Perf</strong></TableCell>
                      <TableCell align="right"><strong>3M Perf</strong></TableCell>
                      <TableCell align="right"><strong>6M Perf</strong></TableCell>
                      <TableCell align="center"><strong>BB Streak</strong></TableCell>
                      <TableCell align="center"><strong>DMI Streak</strong></TableCell>
                      <TableCell align="center"><strong>Combined</strong></TableCell>
                    </>
                  )}
                  <TableCell align="center"><strong>Actions</strong></TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
              {loading ? (
                // Loading skeleton
                Array.from({ length: 5 }).map((_, index) => (
                  <TableRow key={index}>
                    <TableCell><Skeleton width={40} /></TableCell>
                    <TableCell><Skeleton width={80} /></TableCell>
                    <TableCell><Skeleton width={100} /></TableCell>
                    <TableCell><Skeleton width={150} /></TableCell>
                    {!isMobile && (
                      <>
                        <TableCell><Skeleton width={60} /></TableCell>
                        <TableCell><Skeleton width={60} /></TableCell>
                        <TableCell><Skeleton width={60} /></TableCell>
                        <TableCell><Skeleton width={40} /></TableCell>
                        <TableCell><Skeleton width={40} /></TableCell>
                        <TableCell><Skeleton width={40} /></TableCell>
                      </>
                    )}
                    <TableCell><Skeleton width={100} /></TableCell>
                  </TableRow>
                ))
              ) : watchListItems.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={isMobile ? 5 : 11} align="center">
                    <Box py={4}>
                      <Typography variant="h6" color="text.secondary" gutterBottom>
                        No symbols in watch list
                      </Typography>
                      <Typography variant="body2" color="text.secondary" paragraph>
                        Add symbols to track their performance and manage your investment watchlist.
                      </Typography>
                      <Button
                        variant="contained"
                        startIcon={<AddIcon />}
                        onClick={handleOpenAddDialog}
                      >
                        Add First Symbol
                      </Button>
                    </Box>
                  </TableCell>
                </TableRow>
              ) : (
                <SortableContext
                  items={watchListItems.map(item => item.id)}
                  strategy={verticalListSortingStrategy}
                >
                  {watchListItems
                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    .map((item) => (
                      <SortableRow
                        key={item.id}
                        item={item}
                        isMobile={isMobile}
                        onEdit={handleEditClick}
                        onDelete={handleDeleteClick}
                      />
                    ))}
                </SortableContext>
              )}
            </TableBody>
            </Table>
          </DndContext>
        </TableContainer>

        {/* Pagination */}
        {watchListItems.length > 0 && (
          <TablePagination
            rowsPerPageOptions={[10, 25, 50, 100]}
            component="div"
            count={watchListItems.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={(_, newPage) => setPage(newPage)}
            onRowsPerPageChange={(event) => {
              setRowsPerPage(parseInt(event.target.value, 10));
              setPage(0);
            }}
            labelRowsPerPage="Items per page:"
            labelDisplayedRows={({ from, to, count }) =>
              `${from}-${to} of ${count !== -1 ? count : `more than ${to}`}`
            }
          />
        )}
      </Paper>

      {/* Add Dialog */}
      <Dialog open={addDialogOpen} onClose={() => setAddDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add Symbol to Watch List</DialogTitle>
        <DialogContent>
          <Box display="flex" flexDirection="column" gap={2} pt={1}>
            <Box>
              <Box display="flex" alignItems="center" gap={1} mb={1}>
                <Typography variant="subtitle2">
                  Symbol Selection
                </Typography>
                <Button
                  size="small"
                  variant="outlined"
                  onClick={() => loadAvailableInstruments(true)}
                  disabled={instrumentsLoading}
                  startIcon={instrumentsLoading ? <CircularProgress size={16} /> : <RefreshIcon />}
                >
                  {instrumentsRetryCount > 0 ? `Retry ${instrumentsRetryCount}/2` : 'Refresh'}
                </Button>
                {!instrumentsLoadedOnce && instrumentsRetryCount > 0 && (
                  <Chip
                    size="small"
                    label={`Retrying... (${instrumentsRetryCount}/2)`}
                    color="warning"
                    variant="outlined"
                  />
                )}
              </Box>
              <Autocomplete
                options={availableInstruments.map(instrument => ({
                  label: `${instrument.symbol} - ${instrument.name}`,
                  value: instrument.symbol,
                  instrument: instrument
                }))}
                getOptionLabel={(option) => typeof option === 'string' ? option : option.label}
                value={availableInstruments.map(instrument => ({
                  label: `${instrument.symbol} - ${instrument.name}`,
                  value: instrument.symbol,
                  instrument: instrument
                })).find(option => option.value === formData.symbol) || null}
                onChange={(_, newValue) => {
                  const symbolValue = typeof newValue === 'string' ? newValue : newValue?.value || '';
                  setFormData({ ...formData, symbol: symbolValue });
                  setFormErrors({ ...formErrors, symbol: '' });
                }}
                onInputChange={handleSymbolInputChange}
                loading={instrumentsLoading}
                freeSolo={!instrumentsLoadedOnce || availableInstruments.length === 0}
                filterOptions={(options, { inputValue }) => {
                  // Enhanced filtering to show partial matches
                  const filtered = options.filter(option =>
                    option.label.toLowerCase().includes(inputValue.toLowerCase()) ||
                    option.value.toLowerCase().includes(inputValue.toLowerCase())
                  );
                  return filtered;
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Symbol"
                    placeholder={instrumentsLoadedOnce ? "Search for a symbol..." : "Enter symbol manually (e.g., AAPL)"}
                    error={!!formErrors.symbol}
                    helperText={
                      formErrors.symbol ||
                      (instrumentsLoadedOnce
                        ? `${availableInstruments.length} instruments available`
                        : instrumentsLoading
                          ? "Loading instruments..."
                          : "Enter symbol manually or refresh to load instruments"
                      )
                    }
                    InputProps={{
                      ...params.InputProps,
                      endAdornment: (
                        <>
                          {instrumentsLoading ? <CircularProgress color="inherit" size={20} /> : null}
                          {params.InputProps.endAdornment}
                        </>
                      ),
                    }}
                    onChange={(e) => {
                      // Handle manual input when freeSolo is enabled
                      if (!instrumentsLoadedOnce || availableInstruments.length === 0) {
                        setFormData({ ...formData, symbol: e.target.value.toUpperCase() });
                        setFormErrors({ ...formErrors, symbol: '' });
                      }
                    }}
                  />
                )}
                renderOption={(props, option) => {
                  if (typeof option === 'string') {
                    return (
                      <Box component="li" {...props}>
                        <Typography variant="body2" fontWeight="bold">
                          {option}
                        </Typography>
                      </Box>
                    );
                  }
                  return (
                    <Box component="li" {...props}>
                      <Box>
                        <Typography variant="body2" fontWeight="bold">
                          {option.value}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {option.instrument.name}
                        </Typography>
                      </Box>
                    </Box>
                  );
                }}
                disabled={instrumentsLoading}
                fullWidth
              />
            </Box>

            <TextField
              label="Start Date"
              type="date"
              value={formData.startDate}
              onChange={(e) => {
                setFormData({ ...formData, startDate: e.target.value });
                setFormErrors({ ...formErrors, startDate: '' });
              }}
              InputLabelProps={{ shrink: true }}
              error={!!formErrors.startDate}
              helperText={formErrors.startDate}
              fullWidth
            />

            <TextField
              label="Remarks (Optional)"
              multiline
              rows={3}
              value={formData.remarks}
              onChange={(e) => {
                setFormData({ ...formData, remarks: e.target.value });
                setFormErrors({ ...formErrors, remarks: '' });
              }}
              error={!!formErrors.remarks}
              helperText={formErrors.remarks || 'Max 128 characters'}
              inputProps={{ maxLength: 128 }}
              fullWidth
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddDialogOpen(false)} disabled={submitting}>
            Cancel
          </Button>
          <Button
            onClick={handleAddSymbol}
            variant="contained"
            disabled={submitting}
          >
            {submitting ? 'Adding...' : 'Add Symbol'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Edit Watch List Item</DialogTitle>
        <DialogContent>
          <Box display="flex" flexDirection="column" gap={2} pt={1}>
            <TextField
              label="Symbol"
              value={formData.symbol}
              disabled
              fullWidth
            />

            <TextField
              label="Start Date"
              value={new Date(formData.startDate).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: '2-digit'
              })}
              disabled
              fullWidth
              helperText="Start date cannot be changed after creation"
            />

            <TextField
              label="Remarks (Optional)"
              multiline
              rows={3}
              value={formData.remarks}
              onChange={(e) => {
                setFormData({ ...formData, remarks: e.target.value });
                setFormErrors({ ...formErrors, remarks: '' });
              }}
              error={!!formErrors.remarks}
              helperText={formErrors.remarks || 'Max 128 characters'}
              inputProps={{ maxLength: 128 }}
              fullWidth
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)} disabled={submitting}>
            Cancel
          </Button>
          <Button
            onClick={handleEditSymbol}
            variant="contained"
            disabled={submitting}
          >
            {submitting ? 'Updating...' : 'Update'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          <Box display="flex" alignItems="center" gap={1}>
            <DeleteIcon color="error" />
            Remove from Watch List
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography paragraph>
            Are you sure you want to remove <strong>{selectedItem?.symbol}</strong> from your watch list?
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            This will remove the symbol from your watch list but will not affect:
          </Typography>
          <Box component="ul" sx={{ margin: '8px 0', paddingLeft: '20px' }}>
            <Typography component="li" variant="body2" color="text.secondary">
              Historical OHLCV data
            </Typography>
            <Typography component="li" variant="body2" color="text.secondary">
              Technical indicators
            </Typography>
            <Typography component="li" variant="body2" color="text.secondary">
              Position records
            </Typography>
          </Box>
          <Alert severity="info" sx={{ mt: 2 }}>
            <Typography variant="body2">
              You can always add this symbol back to your watch list later.
            </Typography>
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)} disabled={submitting}>
            Cancel
          </Button>
          <Button
            onClick={handleDeleteSymbol}
            color="error"
            variant="contained"
            disabled={submitting}
            startIcon={submitting ? <CircularProgress size={20} /> : <DeleteIcon />}
          >
            {submitting ? 'Removing...' : 'Remove from Watch List'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Update OHLCV Data Confirmation Dialog */}
      <Dialog open={showUpdateConfirmation} onClose={() => setShowUpdateConfirmation(false)}>
        <DialogTitle>Update OHLCV Data</DialogTitle>
        <DialogContent>
          <Typography paragraph>
            This will update OHLCV (Open, High, Low, Close, Volume) historical data for all <strong>{watchListItems.length}</strong> symbols in your watch list.
          </Typography>
          <Typography paragraph>
            The process will also automatically recalculate technical indicators (Bollinger Bands and DMI) using incremental mode.
          </Typography>
          <Alert severity="info" sx={{ mt: 2 }}>
            <Typography variant="body2">
              This operation may take several minutes to complete depending on the number of symbols and available data updates.
            </Typography>
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowUpdateConfirmation(false)}>
            Cancel
          </Button>
          <Button
            onClick={confirmUpdateOHLCVData}
            variant="contained"
            color="primary"
          >
            Start Update
          </Button>
        </DialogActions>
      </Dialog>

      {/* Update Progress Dialog */}
      <WatchListUpdateProgressDialog
        open={showUpdateDialog}
        onClose={handleCloseUpdateDialog}
        isComplete={!updatingOHLCV}
        result={updateResult || undefined}
        startTime={updateStartTime || undefined}
        title="Update OHLCV Data & Technical Indicators"
      />

      {/* Success/Error Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default WatchList;
