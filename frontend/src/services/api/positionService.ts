import { get, post, put, del, postLongRunning } from './apiClient';
import {
  Position,
  CreatePositionRequest,
  UpdatePositionRequest,
  PositionsUpdateRequest,
  PositionsUpdateResult,
  PnLRecalculationRequest,
  ApiResponse
} from '../../types/api';

export class PositionService {

  /**
   * Get all positions with optional filtering
   */
  static async getPositions(
    symbol?: string,
    status?: 'OPEN' | 'CLOSED',
    side?: 'BUY' | 'SELL'
  ): Promise<ApiResponse<Position[]>> {
    const params = new URLSearchParams();
    if (symbol) params.append('symbol', symbol);
    if (status) params.append('status', status);
    if (side) params.append('side', side);

    const queryString = params.toString();
    return get<Position[]>(`/positions${queryString ? `?${queryString}` : ''}`);
  }

  /**
   * Get position by ID
   */
  static async getPositionById(id: number): Promise<ApiResponse<Position>> {
    return get<Position>(`/positions/${id}`);
  }

  /**
   * Create new position
   */
  static async createPosition(request: CreatePositionRequest): Promise<ApiResponse<Position>> {
    return post<Position>('/positions', request);
  }

  /**
   * Update existing position
   */
  static async updatePosition(id: number, request: UpdatePositionRequest): Promise<ApiResponse<Position>> {
    return put<Position>(`/positions/${id}`, request);
  }

  /**
   * Close position
   */
  static async closePosition(id: number): Promise<ApiResponse<Position>> {
    return post<Position>(`/positions/${id}/close`, {});
  }

  /**
   * Delete position
   */
  static async deletePosition(id: number): Promise<ApiResponse<void>> {
    return del<void>(`/positions/${id}`);
  }

  /**
   * Update position price
   */
  static async updatePositionPrice(id: number, price: number): Promise<ApiResponse<Position>> {
    return put<Position>(`/positions/${id}/price?price=${price}`, {});
  }

  /**
   * Update close price with automatic P&L calculation
   */
  static async updateClosePrice(id: number, closePrice: number): Promise<ApiResponse<Position>> {
    return put<Position>(`/positions/${id}/close-price?closePrice=${closePrice}`, {});
  }

  /**
   * Get open positions
   */
  static async getOpenPositions(): Promise<ApiResponse<Position[]>> {
    return get<Position[]>('/positions/open');
  }

  /**
   * Get closed positions
   */
  static async getClosedPositions(): Promise<ApiResponse<Position[]>> {
    return this.getPositions(undefined, 'CLOSED', undefined);
  }

  /**
   * Get positions that should be stopped out
   */
  static async getPositionsToStopOut(): Promise<ApiResponse<Position[]>> {
    return get<Position[]>('/positions/stop-out');
  }

  /**
   * Get positions by symbol
   */
  static async getPositionsBySymbol(symbol: string): Promise<ApiResponse<Position[]>> {
    return this.getPositions(symbol, undefined, undefined);
  }

  /**
   * Update OHLCV data for all position symbols and recalculate P&L
   */
  static async updateOHLCVDataForPositions(
    request: PositionsUpdateRequest = {}
  ): Promise<ApiResponse<PositionsUpdateResult>> {
    // Use long-running endpoint for potentially time-consuming operations
    return postLongRunning<PositionsUpdateResult>('/positions/update-ohlcv-data', request);
  }

  /**
   * Recalculate P&L for all positions using latest market prices
   */
  static async recalculatePnLForAllPositions(
    request: PnLRecalculationRequest = {}
  ): Promise<ApiResponse<{ updatedCount: number; message: string; stopLossMode: string }>> {
    return post<{ updatedCount: number; message: string; stopLossMode: string }>('/positions/recalculate-pnl', request);
  }
}
